"""
T-1节拍器配置管理模块
"""

class Config:
    """应用程序配置类"""
    
    # 显示配置
    WINDOW_WIDTH = 360
    WINDOW_HEIGHT = 360
    WINDOW_TITLE = "T-1节拍器"
    
    # 生产配置
    DEFAULT_TARGET = 100  # 默认10分钟目标产量
    UPDATE_INTERVAL = 1000  # 数据更新间隔(毫秒)
    COUNTDOWN_DURATION = 600  # 10分钟倒计时(秒)
    
    # 颜色主题
    COLORS = {
        'success': '#4CCD99',      # 绿色 - 超额完成
        'warning': '#FFD700',      # 金色 - 接近目标
        'danger': '#FF6B6B',       # 红色 - 需要加速
        'primary': '#2196F3',      # 蓝色 - 当前产量
        'background': '#1E1E1E',   # 深色背景
        'text_light': '#FFFFFF',   # 白色文字
        'text_dark': '#000000',    # 黑色文字
        'border': '#333333'        # 边框颜色
    }
    
    # 字体配置 (适配小界面)
    FONTS = {
        'main_number': ('Arial Black', 36, True),  # 主数字字体
        'target_number': ('Arial', 24, False),    # 目标数字字体
        'hint_text': ('Arial', 12, False),        # 提示文字字体
        'timer_text': ('Arial', 16, True),        # 计时器字体
        'button_text': ('Arial', 10, False)       # 按钮字体
    }
    
    # 动画配置
    ANIMATION = {
        'duration': 500,           # 动画持续时间(毫秒)
        'easing': 'OutCubic',      # 缓动类型
        'celebration_duration': 3000,  # 庆祝动画持续时间
        'pulse_interval': 1000     # 脉冲间隔
    }
    
    # 音效配置
    SOUNDS = {
        'enabled': True,
        'volume': 0.7,
        'milestone_sound': 'assets/sounds/milestone.wav',
        'success_sound': 'assets/sounds/success.wav',
        'warning_sound': 'assets/sounds/warning.wav'
    }
    
    # 阈值配置
    THRESHOLDS = {
        'success': 1.0,    # 100%及以上为成功
        'warning': 0.8,    # 80%以上为警告
        'danger': 0.6,     # 60%以下为危险
        'sprint_mode': 0.8  # 最后2分钟且低于80%进入冲刺模式
    }

class ThemeConfig:
    """主题配置类"""
    
    LIGHT_THEME = {
        'background': '#F5F5F5',
        'surface': '#FFFFFF',
        'primary': '#2196F3',
        'text': '#212121',
        'border': '#E0E0E0'
    }
    
    DARK_THEME = {
        'background': '#1E1E1E',
        'surface': '#2D2D2D',
        'primary': '#2196F3',
        'text': '#FFFFFF',
        'border': '#333333'
    }
    
    HIGH_CONTRAST_THEME = {
        'background': '#000000',
        'surface': '#1A1A1A',
        'primary': '#FFFF00',
        'text': '#FFFFFF',
        'border': '#FFFFFF'
    }
