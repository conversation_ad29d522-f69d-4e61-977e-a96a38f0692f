"""
T-1节拍器配置管理模块
"""

class Config:
    """应用程序配置类"""
    
    # 显示配置
    WINDOW_WIDTH = 360
    WINDOW_HEIGHT = 360
    WINDOW_TITLE = "T-1节拍器"
    
    # 生产配置
    DEFAULT_TARGET = 100  # 默认10分钟目标产量
    UPDATE_INTERVAL = 1000  # 数据更新间隔(毫秒)
    COUNTDOWN_DURATION = 600  # 10分钟倒计时(秒)
    
    # 颜色主题
    COLORS = {
        'success': '#4CCD99',      # 绿色 - 超额完成
        'warning': '#FFD700',      # 金色 - 接近目标
        'danger': '#FF6B6B',       # 红色 - 需要加速
        'primary': '#2196F3',      # 蓝色 - 当前产量
        'background': '#1E1E1E',   # 深色背景
        'text_light': '#FFFFFF',   # 白色文字
        'text_dark': '#000000',    # 黑色文字
        'border': '#333333'        # 边框颜色
    }
    
    # 字体配置 (适配小界面)
    FONTS = {
        'main_number': ('Arial Black', 36, True),  # 主数字字体
        'target_number': ('Arial', 24, False),    # 目标数字字体
        'hint_text': ('Arial', 12, False),        # 提示文字字体
        'timer_text': ('Arial', 16, True),        # 计时器字体
        'button_text': ('Arial', 10, False)       # 按钮字体
    }
    
    # 动画配置
    ANIMATION = {
        'duration': 500,           # 动画持续时间(毫秒)
        'easing': 'OutCubic',      # 缓动类型
        'celebration_duration': 3000,  # 庆祝动画持续时间
        'pulse_interval': 1000     # 脉冲间隔
    }
    
    # 音效配置
    SOUNDS = {
        'enabled': True,
        'volume': 0.7,
        'milestone_sound': 'assets/sounds/milestone.wav',
        'success_sound': 'assets/sounds/success.wav',
        'warning_sound': 'assets/sounds/warning.wav'
    }
    
    # 阈值配置
    THRESHOLDS = {
        'success': 1.0,    # 100%及以上为成功
        'warning': 0.8,    # 80%以上为警告
        'danger': 0.6,     # 60%以下为危险
        'sprint_mode': 0.8  # 最后2分钟且低于80%进入冲刺模式
    }

class ThemeConfig:
    """主题配置类"""

    # 深色主题 (默认)
    DARK_THEME = {
        'name': '深色主题',
        'background': '#1E1E1E',
        'surface': '#2D2D2D',
        'primary': '#2196F3',
        'success': '#4CCD99',
        'warning': '#FFD700',
        'danger': '#FF6B6B',
        'text_light': '#FFFFFF',
        'text_dark': '#000000',
        'border': '#333333'
    }

    # 浅色主题
    LIGHT_THEME = {
        'name': '浅色主题',
        'background': '#F5F5F5',
        'surface': '#FFFFFF',
        'primary': '#1976D2',
        'success': '#388E3C',
        'warning': '#F57C00',
        'danger': '#D32F2F',
        'text_light': '#FFFFFF',
        'text_dark': '#212121',
        'border': '#E0E0E0'
    }

    # 高对比度主题
    HIGH_CONTRAST_THEME = {
        'name': '高对比度',
        'background': '#000000',
        'surface': '#1A1A1A',
        'primary': '#FFFF00',
        'success': '#00FF00',
        'warning': '#FFFF00',
        'danger': '#FF0000',
        'text_light': '#FFFFFF',
        'text_dark': '#000000',
        'border': '#FFFFFF'
    }

    # 蓝色主题
    BLUE_THEME = {
        'name': '蓝色主题',
        'background': '#0D47A1',
        'surface': '#1565C0',
        'primary': '#42A5F5',
        'success': '#66BB6A',
        'warning': '#FFCA28',
        'danger': '#EF5350',
        'text_light': '#FFFFFF',
        'text_dark': '#000000',
        'border': '#1976D2'
    }

    # 绿色主题
    GREEN_THEME = {
        'name': '绿色主题',
        'background': '#1B5E20',
        'surface': '#2E7D32',
        'primary': '#4CAF50',
        'success': '#81C784',
        'warning': '#FFB74D',
        'danger': '#E57373',
        'text_light': '#FFFFFF',
        'text_dark': '#000000',
        'border': '#388E3C'
    }

    # 所有主题列表
    ALL_THEMES = [DARK_THEME, LIGHT_THEME, HIGH_CONTRAST_THEME, BLUE_THEME, GREEN_THEME]

    @classmethod
    def get_theme_by_name(cls, name):
        """根据名称获取主题"""
        for theme in cls.ALL_THEMES:
            if theme['name'] == name:
                return theme
        return cls.DARK_THEME  # 默认返回深色主题
