"""
T-1节拍器配置管理模块
"""

class Config:
    """应用程序配置类"""
    
    # 显示配置
    WINDOW_WIDTH = 360
    WINDOW_HEIGHT = 360
    WINDOW_TITLE = "T-1节拍器"
    
    # 生产配置
    DEFAULT_TARGET = 100  # 默认10分钟目标产量
    UPDATE_INTERVAL = 1000  # 数据更新间隔(毫秒)
    COUNTDOWN_DURATION = 600  # 10分钟倒计时(秒)
    
    # 颜色主题 (将由主题管理器动态更新)
    COLORS = {
        'success': '#4CCD99',      # 绿色 - 超额完成
        'warning': '#FFD700',      # 金色 - 接近目标
        'danger': '#FF6B6B',       # 红色 - 需要加速
        'primary': '#2196F3',      # 蓝色 - 当前产量
        'primary_light': '#64B5F6', # 浅蓝色
        'background': '#1E1E1E',   # 深色背景
        'surface': '#2D2D2D',      # 表面颜色
        'card_bg': '#3A3A3A',      # 卡片背景
        'text_primary': '#FFFFFF', # 主文字颜色
        'text_secondary': '#B0B0B0', # 次文字颜色
        'text_on_primary': '#FFFFFF', # 主色上的文字
        'text_on_surface': '#FFFFFF', # 表面上的文字
        'border': '#404040',       # 边框颜色
        'border_light': '#555555', # 浅边框颜色
        # 保持向后兼容
        'text_light': '#FFFFFF',   # 白色文字
        'text_dark': '#000000',    # 黑色文字
    }
    
    # 字体配置 (适配小界面)
    FONTS = {
        'main_number': ('Arial Black', 36, True),  # 主数字字体
        'target_number': ('Arial', 24, False),    # 目标数字字体
        'hint_text': ('Arial', 12, False),        # 提示文字字体
        'timer_text': ('Arial', 16, True),        # 计时器字体
        'button_text': ('Arial', 10, False)       # 按钮字体
    }
    
    # 动画配置
    ANIMATION = {
        'duration': 500,           # 动画持续时间(毫秒)
        'easing': 'OutCubic',      # 缓动类型
        'celebration_duration': 3000,  # 庆祝动画持续时间
        'pulse_interval': 1000     # 脉冲间隔
    }
    
    # 音效配置
    SOUNDS = {
        'enabled': True,
        'volume': 0.7,
        'milestone_sound': 'assets/sounds/milestone.wav',
        'success_sound': 'assets/sounds/success.wav',
        'warning_sound': 'assets/sounds/warning.wav'
    }
    
    # 阈值配置
    THRESHOLDS = {
        'success': 1.0,    # 100%及以上为成功
        'warning': 0.8,    # 80%以上为警告
        'danger': 0.6,     # 60%以下为危险
        'sprint_mode': 0.8  # 最后2分钟且低于80%进入冲刺模式
    }

class ThemeConfig:
    """主题配置类"""

    # 深色主题 (默认)
    DARK_THEME = {
        'name': '深色主题',
        'background': '#1E1E1E',
        'surface': '#2D2D2D',
        'card_bg': '#3A3A3A',
        'primary': '#2196F3',
        'primary_light': '#64B5F6',
        'success': '#4CCD99',
        'warning': '#FFD700',
        'danger': '#FF6B6B',
        'text_primary': '#FFFFFF',
        'text_secondary': '#B0B0B0',
        'text_on_primary': '#FFFFFF',
        'text_on_surface': '#FFFFFF',
        'border': '#404040',
        'border_light': '#555555'
    }

    # 浅色主题
    LIGHT_THEME = {
        'name': '浅色主题',
        'background': '#FAFAFA',
        'surface': '#FFFFFF',
        'card_bg': '#F5F5F5',
        'primary': '#1976D2',
        'primary_light': '#42A5F5',
        'success': '#388E3C',
        'warning': '#F57C00',
        'danger': '#D32F2F',
        'text_primary': '#212121',
        'text_secondary': '#757575',
        'text_on_primary': '#FFFFFF',
        'text_on_surface': '#212121',
        'border': '#E0E0E0',
        'border_light': '#F0F0F0'
    }

    # 高对比度主题
    HIGH_CONTRAST_THEME = {
        'name': '高对比度',
        'background': '#000000',
        'surface': '#1A1A1A',
        'card_bg': '#2A2A2A',
        'primary': '#FFFF00',
        'primary_light': '#FFFF80',
        'success': '#00FF00',
        'warning': '#FFFF00',
        'danger': '#FF0000',
        'text_primary': '#FFFFFF',
        'text_secondary': '#CCCCCC',
        'text_on_primary': '#000000',
        'text_on_surface': '#FFFFFF',
        'border': '#FFFFFF',
        'border_light': '#CCCCCC'
    }

    # 蓝色海洋主题
    BLUE_THEME = {
        'name': '蓝色海洋',
        'background': '#0D47A1',
        'surface': '#1565C0',
        'card_bg': '#1976D2',
        'primary': '#1976D2',  # 使用更深的蓝色提高对比度
        'primary_light': '#42A5F5',
        'success': '#66BB6A',
        'warning': '#FFCA28',
        'danger': '#EF5350',
        'text_primary': '#FFFFFF',
        'text_secondary': '#E3F2FD',
        'text_on_primary': '#FFFFFF',
        'text_on_surface': '#FFFFFF',
        'border': '#2196F3',
        'border_light': '#64B5F6'
    }

    # 绿色森林主题
    GREEN_THEME = {
        'name': '绿色森林',
        'background': '#1B5E20',
        'surface': '#2E7D32',
        'card_bg': '#388E3C',
        'primary': '#2E7D32',  # 使用更深的绿色提高对比度
        'primary_light': '#4CAF50',
        'success': '#66BB6A',
        'warning': '#FFB74D',
        'danger': '#EF5350',
        'text_primary': '#FFFFFF',
        'text_secondary': '#E8F5E8',
        'text_on_primary': '#FFFFFF',
        'text_on_surface': '#FFFFFF',
        'border': '#4CAF50',
        'border_light': '#81C784'
    }

    # 紫色梦幻主题
    PURPLE_THEME = {
        'name': '紫色梦幻',
        'background': '#4A148C',
        'surface': '#6A1B9A',
        'card_bg': '#7B1FA2',
        'primary': '#9C27B0',
        'primary_light': '#BA68C8',
        'success': '#66BB6A',
        'warning': '#FFB74D',
        'danger': '#EF5350',
        'text_primary': '#FFFFFF',
        'text_secondary': '#F3E5F5',
        'text_on_primary': '#FFFFFF',
        'text_on_surface': '#FFFFFF',
        'border': '#9C27B0',
        'border_light': '#BA68C8'
    }

    # 所有主题列表
    ALL_THEMES = [DARK_THEME, LIGHT_THEME, HIGH_CONTRAST_THEME, BLUE_THEME, GREEN_THEME, PURPLE_THEME]

    @classmethod
    def get_theme_by_name(cls, name):
        """根据名称获取主题"""
        for theme in cls.ALL_THEMES:
            if theme['name'] == name:
                return theme
        return cls.DARK_THEME  # 默认返回深色主题
