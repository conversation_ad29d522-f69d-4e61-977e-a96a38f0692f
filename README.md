# T-1节拍器 - 制造业生产线产量对比仪表盘

## 项目简介

T-1节拍器是一个专为制造业生产线设计的产量对比仪表盘程序，具有强大的视觉表现力和实时数据反馈功能。

## 核心功能

### 🎯 动态双数字显示
- **当前10分钟产量**: 超大LED字体显示，默认蓝色
- **目标值**: 金色边框，稍小字号
- **智能连接符**: `→` 或 `VS` 符号，根据差值动态变色

### 📊 进度可视化
- **环形进度条**: 包围中央数字，支持渐变色和粒子光点流动
- **柱状对比图**: 底部辅助显示
- **实时动画**: 进度填充伴随粒子溅射效果

### 🎨 智能颜色反馈系统
- **绿色**: 超额完成 (≥100%)
- **金色**: 接近目标 (80-99%)
- **红色**: 需要加速 (<80%)
- **渐变色**: 红黄渐变表示进度状态

### ✨ 特效增强
- **实时微交互**: 数字弹跳动画、进度条粒子效果
- **里程碑庆祝**: 目标达成时的爆炸粒子效果
- **冲刺模式**: 最后2分钟低于80%时的警报样式
- **呼吸闪烁**: 倒计时最后1分钟的边框效果

## 技术架构

### 技术栈
- **GUI框架**: PyQt6
- **图形渲染**: QPainter + QPropertyAnimation
- **数据处理**: 多线程 + QTimer
- **音效支持**: pygame (可选)

### 项目结构
```
T-1/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── ui/                    # UI组件模块
│   ├── __init__.py
│   ├── main_window.py     # 主窗口类
│   ├── comparison_widget.py # 核心对比显示组件
│   ├── progress_ring.py   # 自定义环形进度条
│   └── celebration.py     # 庆祝动画效果
├── core/                  # 核心逻辑模块
│   ├── __init__.py
│   ├── data_manager.py    # 数据管理和模拟
│   ├── color_theme.py     # 颜色主题系统
│   └── config.py         # 配置管理
└── assets/               # 资源文件
    ├── sounds/           # 音效文件
    └── fonts/            # 字体文件
```

## 安装和运行

### 环境要求
- Python 3.8+
- PyQt6
- numpy
- pygame (可选，用于音效)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd T-1
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

## 使用说明

### 基本操作
1. **设置目标产量**: 在控制面板中调整目标值
2. **开始生产周期**: 点击"开始"按钮启动10分钟倒计时
3. **监控进度**: 观察实时产量对比和环形进度条
4. **手动计数**: 使用"手动+1"按钮模拟产量增加
5. **暂停/继续**: 可随时暂停和恢复生产周期

### 快捷键
- `Ctrl+N`: 新建周期
- `F11`: 切换全屏模式
- `Ctrl+Q`: 退出程序

### 视觉反馈说明
- **绿色背景**: 已达成或超额完成目标
- **金色背景**: 接近目标 (80%以上)
- **红色背景**: 需要加速 (低于80%)
- **粒子效果**: 里程碑达成时的庆祝动画
- **脉冲闪烁**: 最后1分钟的紧急提醒

## 配置选项

### 主要配置 (core/config.py)
- `DEFAULT_TARGET`: 默认目标产量
- `UPDATE_INTERVAL`: 数据更新间隔
- `COUNTDOWN_DURATION`: 倒计时时长
- `COLORS`: 颜色主题配置
- `FONTS`: 字体配置
- `ANIMATION`: 动画参数

### 主题切换
程序支持多种主题模式:
- 深色主题 (默认)
- 浅色主题
- 高对比度主题

## 开发说明

### 扩展功能
- 数据导出功能
- 历史记录查看
- 网络数据接口
- 多生产线支持
- 自定义音效

### 性能优化
- 使用QPropertyAnimation实现流畅动画
- 粒子系统优化减少CPU占用
- 智能重绘机制提升响应速度

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请联系开发团队。

---

**T-1节拍器** - 让生产数据可视化更加直观和高效！
