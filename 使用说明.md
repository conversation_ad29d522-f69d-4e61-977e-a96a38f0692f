# T-1节拍器使用说明

## 🎯 程序概述

T-1节拍器是一个专为制造业生产线设计的产量对比仪表盘，具有以下特点：

- **实时产量监控**: 10分钟周期内的产量实时对比
- **智能视觉反馈**: 根据进度自动变化的颜色主题
- **动态进度显示**: 环形进度条配合粒子动画效果
- **庆祝动画系统**: 里程碑达成时的视觉庆祝效果
- **数据模拟功能**: 内置生产数据模拟器

## 🚀 快速开始

### 1. 启动程序
```bash
python main.py
```

### 2. 界面说明

#### 主要显示区域
- **左侧大数字**: 当前10分钟产量（蓝色边框）
- **中间符号**: `→` 连接符，颜色随进度变化
- **右侧数字**: 目标产量（金色边框）
- **环形进度条**: 包围整体的动态进度环
- **状态提示**: 显示当前状态的文字提示

#### 控制面板
- **目标产量设置**: 调整当前周期的目标值
- **开始按钮**: 启动新的10分钟生产周期
- **暂停/继续**: 暂停或恢复当前周期
- **停止按钮**: 结束当前周期
- **手动+1**: 手动增加产量计数

#### 信息显示
- **倒计时**: 右上角显示剩余时间
- **进度百分比**: 底部左侧显示完成百分比
- **生产速率**: 底部中间显示当前生产速率
- **预估产量**: 底部右侧显示预估最终产量

## 🎨 视觉反馈系统

### 颜色含义
| 颜色 | 进度范围 | 含义 | 提示文字 |
|------|----------|------|----------|
| 🟢 绿色 | ≥100% | 超额完成 | "✔ 超额完成!" |
| 🟡 金色 | 80-99% | 接近目标 | "➤ 接近目标!" |
| 🔴 红色 | <80% | 需要加速 | "✘ 加速中..." |

### 特殊模式
- **冲刺模式**: 最后2分钟且进度<80%时激活
  - 红色警报样式
  - 快速脉冲效果
  - "冲刺模式!"提示

- **最后1分钟**: 倒计时呼吸闪烁效果

## ✨ 动画效果

### 1. 数字弹跳
- 产量增加时数字会有弹跳动画
- 弹跳幅度随超出比例增大

### 2. 进度环动画
- 平滑的进度填充动画
- 粒子光点沿环流动
- 渐变色随进度变化

### 3. 庆祝效果
- **里程碑庆祝**: 每达到20%进度触发小型粒子效果
- **目标达成**: 爆炸式粒子效果 + "目标达成!"文字
- **冲刺警告**: 红色粒子 + "冲刺模式!"文字

## 🎮 操作指南

### 基本操作流程
1. **设置目标**: 在控制面板调整目标产量
2. **开始周期**: 点击"开始"按钮
3. **监控进度**: 观察实时数据和视觉反馈
4. **手动计数**: 可使用"手动+1"模拟产量增加
5. **查看结果**: 周期结束后查看完成情况

### 快捷键
- `Ctrl+N`: 新建周期
- `F11`: 切换全屏模式
- `Ctrl+Q`: 退出程序

### 菜单功能
- **文件菜单**: 新建周期、退出
- **视图菜单**: 全屏切换
- **帮助菜单**: 关于信息

## 📊 数据说明

### 模拟模式
程序内置智能数据模拟器，特点：
- **基础生产速率**: 约0.15件/秒
- **随机变化**: ±0.05件/秒的波动
- **突发生产**: 10%概率的2倍速率
- **时间压力**: 最后2分钟自动加速

### 实际数据接入
程序预留了数据接口，可以：
- 替换模拟数据源
- 接入实际生产线数据
- 支持多种数据格式

## 🔧 配置选项

### 主要配置文件: `core/config.py`

#### 基本设置
```python
DEFAULT_TARGET = 100        # 默认目标产量
UPDATE_INTERVAL = 1000      # 更新间隔(毫秒)
COUNTDOWN_DURATION = 600    # 周期时长(秒)
```

#### 颜色主题
```python
COLORS = {
    'success': '#4CCD99',   # 成功绿色
    'warning': '#FFD700',   # 警告金色
    'danger': '#FF6B6B',    # 危险红色
    'primary': '#2196F3',   # 主要蓝色
    # ...更多颜色配置
}
```

#### 阈值设置
```python
THRESHOLDS = {
    'success': 1.0,     # 100%为成功
    'warning': 0.8,     # 80%为警告
    'danger': 0.6,      # 60%为危险
    'sprint_mode': 0.8  # 冲刺模式阈值
}
```

## 🛠️ 故障排除

### 常见问题

#### 1. 程序无法启动
- 检查Python版本 (需要3.8+)
- 安装依赖: `pip install -r requirements.txt`
- 检查PyQt6安装: `pip install PyQt6`

#### 2. 界面显示异常
- 尝试切换全屏模式 (F11)
- 重启程序
- 检查显示器分辨率设置

#### 3. 动画卡顿
- 关闭其他占用GPU的程序
- 降低动画复杂度（修改配置文件）
- 检查系统性能

### 性能优化
- 程序使用硬件加速渲染
- 智能重绘机制减少CPU占用
- 粒子系统自动优化

## 📈 扩展功能

### 计划中的功能
- [ ] 数据导出功能
- [ ] 历史记录查看
- [ ] 多生产线支持
- [ ] 网络数据接口
- [ ] 自定义音效
- [ ] 报表生成

### 自定义开发
程序采用模块化设计，支持：
- 自定义颜色主题
- 扩展动画效果
- 添加新的数据源
- 修改界面布局

## 📞 技术支持

如遇到问题或需要技术支持，请：
1. 查看本使用说明
2. 运行测试程序: `python test_basic.py`
3. 检查错误日志
4. 联系开发团队

---

**T-1节拍器** - 让生产数据可视化更加直观高效！
