"""
T-1节拍器主窗口
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QMenuBar, QStatusBar, QPushButton, QSpinBox, 
                            QLabel, QFrame, QMessageBox, QToolBar)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QAction, QIcon, QFont
from core.config import Config
from core.data_manager import ProductionDataManager
from core.theme_manager import ThemeManager
from ui.comparison_widget import ComparisonWidget

class MainWindow(QMainWindow):
    """T-1节拍器主窗口"""
    
    def __init__(self):
        super().__init__()

        # 管理器
        self.data_manager = ProductionDataManager()
        self.theme_manager = ThemeManager()
        self.setup_data_connections()

        # UI初始化
        self.setup_ui()
        self.setup_status_bar()

        # 应用配置
        self.apply_config()

        # 连接主题变更信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)

        # 程序启动后自动开始监控
        print("程序启动，自动开始生产监控")
    
    def setup_ui(self):
        """设置UI"""
        # 中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 紧凑布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # 对比显示组件
        self.comparison_widget = ComparisonWidget()
        layout.addWidget(self.comparison_widget)

        # 控制面板 - 简化版
        self.create_compact_control_panel(layout)

    def create_compact_control_panel(self, layout):
        """创建紧凑控制面板"""
        control_frame = QFrame()
        control_frame.setMaximumHeight(80)
        control_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Config.COLORS['background']};
                border: 1px solid {Config.COLORS['border']};
                border-radius: 5px;
                padding: 5px;
            }}
        """)

        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(3)
        control_layout.setContentsMargins(5, 5, 5, 5)

        # 第一行：目标设置
        target_row = QHBoxLayout()

        target_label = QLabel("目标:")
        target_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")
        target_label.setMaximumWidth(30)

        self.target_spinbox = QSpinBox()
        self.target_spinbox.setRange(1, 999)
        self.target_spinbox.setValue(Config.DEFAULT_TARGET)
        self.target_spinbox.setMaximumWidth(60)
        self.target_spinbox.setStyleSheet(f"""
            QSpinBox {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
                border: 1px solid {Config.COLORS['border']};
                border-radius: 3px;
                padding: 2px;
                font-size: 10px;
            }}
        """)

        # 确定按钮
        self.confirm_target_button = QPushButton("确定")
        self.confirm_target_button.setMaximumWidth(40)
        self.confirm_target_button.clicked.connect(self.on_confirm_target)

        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.setMaximumWidth(40)
        self.reset_button.clicked.connect(self.on_reset_clicked)

        # 手动+1按钮
        self.manual_button = QPushButton("+1")
        self.manual_button.setMaximumWidth(30)
        self.manual_button.clicked.connect(self.on_manual_add)

        # 主题切换按钮
        self.theme_button = QPushButton("🎨")
        self.theme_button.setMaximumWidth(25)
        self.theme_button.clicked.connect(self.on_theme_switch)
        self.theme_button.setToolTip("切换主题")

        # 应用按钮样式
        button_style = f"""
            QPushButton {{
                background-color: {Config.COLORS['primary']};
                color: {Config.COLORS['text_light']};
                border: none;
                border-radius: 3px;
                padding: 3px;
                font-size: 9px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #1976D2;
            }}
            QPushButton:pressed {{
                background-color: #0D47A1;
            }}
            QPushButton:disabled {{
                background-color: {Config.COLORS['border']};
                color: #666666;
            }}
        """

        for button in [self.confirm_target_button, self.reset_button, self.manual_button, self.theme_button]:
            button.setStyleSheet(button_style)

        target_row.addWidget(target_label)
        target_row.addWidget(self.target_spinbox)
        target_row.addWidget(self.confirm_target_button)
        target_row.addStretch()
        target_row.addWidget(self.reset_button)
        target_row.addWidget(self.manual_button)
        target_row.addWidget(self.theme_button)

        control_layout.addLayout(target_row)

        layout.addWidget(control_frame)

    def create_control_panel(self, layout):
        """创建控制面板"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Shape.Box)
        control_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Config.COLORS['background']};
                border: 2px solid {Config.COLORS['border']};
                border-radius: 10px;
                padding: 10px;
            }}
        """)
        
        control_layout = QHBoxLayout(control_frame)
        
        # 目标设置
        target_label = QLabel("目标产量:")
        target_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        self.target_spinbox = QSpinBox()
        self.target_spinbox.setRange(1, 9999)
        self.target_spinbox.setValue(Config.DEFAULT_TARGET)
        self.target_spinbox.setStyleSheet(f"""
            QSpinBox {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
                border: 2px solid {Config.COLORS['border']};
                border-radius: 5px;
                padding: 5px;
                font-size: 14px;
            }}
        """)
        self.target_spinbox.valueChanged.connect(self.on_target_changed)
        
        # 控制按钮
        self.start_button = QPushButton("开始")
        self.start_button.clicked.connect(self.on_start_clicked)
        
        self.pause_button = QPushButton("暂停")
        self.pause_button.clicked.connect(self.on_pause_clicked)
        self.pause_button.setEnabled(False)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.on_stop_clicked)
        self.stop_button.setEnabled(False)
        
        self.manual_button = QPushButton("手动+1")
        self.manual_button.clicked.connect(self.on_manual_add)
        
        # 应用按钮样式
        button_style = f"""
            QPushButton {{
                background-color: {Config.COLORS['primary']};
                color: {Config.COLORS['text_light']};
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #1976D2;
            }}
            QPushButton:pressed {{
                background-color: #0D47A1;
            }}
            QPushButton:disabled {{
                background-color: {Config.COLORS['border']};
                color: #666666;
            }}
        """
        
        for button in [self.start_button, self.pause_button, self.stop_button, self.manual_button]:
            button.setStyleSheet(button_style)
        
        # 布局
        control_layout.addWidget(target_label)
        control_layout.addWidget(self.target_spinbox)
        control_layout.addStretch()
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.pause_button)
        control_layout.addWidget(self.stop_button)
        control_layout.addStretch()
        control_layout.addWidget(self.manual_button)
        
        layout.addWidget(control_frame)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        new_action = QAction("新建周期(&N)", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_cycle)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        fullscreen_action = QAction("全屏(&F)", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)
        
        # 添加工具栏按钮
        start_action = QAction("开始", self)
        start_action.triggered.connect(self.on_start_clicked)
        toolbar.addAction(start_action)
        
        pause_action = QAction("暂停", self)
        pause_action.triggered.connect(self.on_pause_clicked)
        toolbar.addAction(pause_action)
        
        stop_action = QAction("停止", self)
        stop_action.triggered.connect(self.on_stop_clicked)
        toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        manual_action = QAction("手动+1", self)
        manual_action.triggered.connect(self.on_manual_add)
        toolbar.addAction(manual_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("font-size: 9px;")
        self.status_bar.addWidget(self.status_label)
    
    def setup_data_connections(self):
        """设置数据连接"""
        self.data_manager.data_updated.connect(self.on_data_updated)
        self.data_manager.cycle_completed.connect(self.on_cycle_completed)
        self.data_manager.milestone_reached.connect(self.on_milestone_reached)
    
    def apply_config(self):
        """应用配置"""
        self.setWindowTitle(Config.WINDOW_TITLE)
        self.resize(Config.WINDOW_WIDTH, Config.WINDOW_HEIGHT)
        
        # 应用主题
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
            }}
            QMenuBar {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
                border-bottom: 1px solid {Config.COLORS['border']};
            }}
            QMenuBar::item {{
                background-color: transparent;
                padding: 5px 10px;
            }}
            QMenuBar::item:selected {{
                background-color: {Config.COLORS['primary']};
            }}
            QStatusBar {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
                border-top: 1px solid {Config.COLORS['border']};
            }}
            QToolBar {{
                background-color: {Config.COLORS['background']};
                border: 1px solid {Config.COLORS['border']};
                spacing: 5px;
            }}
        """)
    
    # 事件处理方法
    def on_confirm_target(self):
        """确定目标按钮点击"""
        target = self.target_spinbox.value()
        self.data_manager.set_target(target)

        # 更新显示
        if hasattr(self, 'comparison_widget'):
            time_remaining = self.data_manager.get_current_cycle_info()
            self.comparison_widget.update_data(
                self.data_manager.current_count,
                target,
                time_remaining,
                self.data_manager
            )

        self.status_label.setText(f"目标已更新为: {target}")
        print(f"目标产量已更新为: {target}")

    def on_reset_clicked(self):
        """重置按钮点击"""
        # 停止烟花庆祝
        self.comparison_widget.celebration_widget.stop_fireworks_celebration()

        # 重置当前周期
        self.data_manager.restart_cycle()

        self.status_label.setText("周期已重置")
        print("🔄 周期已重置")

    # 移除旧的开始/暂停/停止方法，程序自动运行

    def on_manual_add(self):
        """手动添加产量"""
        self.data_manager.add_manual_count(1)

    def on_theme_switch(self):
        """主题切换按钮点击"""
        self.theme_manager.switch_to_next_theme()

    def on_target_changed(self, value):
        """目标值改变（仅在输入框中改变，需要点击确定才生效）"""
        # 不自动更新，需要用户点击确定按钮
        pass

    def on_data_updated(self, current, target, time_remaining):
        """数据更新回调"""
        self.comparison_widget.update_data(current, target, time_remaining, self.data_manager)

    def on_cycle_completed(self, final_count, target):
        """周期完成回调"""
        # 如果达成目标，触发烟花庆祝
        if final_count >= target:
            result = "恭喜！目标达成！"
            icon = QMessageBox.Icon.Information

            # 触发烟花庆祝
            self.comparison_widget.celebration_widget.celebrate_success()
            print(f"🎆 目标达成！最终产量: {final_count}/{target}")
        else:
            result = f"未达成目标，还差 {target - final_count} 件"
            icon = QMessageBox.Icon.Warning
            print(f"❌ 未达成目标，最终产量: {final_count}/{target}")

        # 延迟显示对话框，让烟花先开始
        QTimer.singleShot(1000, lambda: QMessageBox(
            icon, "周期完成",
            f"最终产量: {final_count}/{target}\n{result}",
            QMessageBox.StandardButton.Ok, self
        ).exec())

    def on_milestone_reached(self, percentage):
        """里程碑达成回调"""
        self.status_label.setText(f"里程碑: {percentage:.0f}% 完成")

    # 菜单事件
    def new_cycle(self):
        """新建周期"""
        if self.is_running:
            reply = QMessageBox.question(self, "确认", "当前周期正在运行，是否停止并开始新周期？")
            if reply == QMessageBox.StandardButton.Yes:
                self.on_stop_clicked()
            else:
                return

        # 重置显示
        self.comparison_widget.update_data(0, self.target_spinbox.value(), Config.COUNTDOWN_DURATION)
        self.status_label.setText("就绪")

    def toggle_fullscreen(self):
        """切换全屏"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于 T-1节拍器",
                         "T-1节拍器 v1.0\n\n"
                         "专业的制造业生产线产量对比仪表盘\n"
                         "实时监控生产进度，提升生产效率\n\n"
                         "开发：Python + PyQt6")

    def on_theme_changed(self, theme):
        """主题变更回调"""
        self.apply_config()
        # 更新控制面板样式
        self._update_control_panel_styles()
        print(f"主窗口主题已更新: {theme['name']}")

    def _update_control_panel_styles(self):
        """更新控制面板样式"""
        # 更新按钮样式
        button_style = f"""
            QPushButton {{
                background-color: {Config.COLORS['primary']};
                color: {Config.COLORS['text_light']};
                border: none;
                border-radius: 3px;
                padding: 3px;
                font-size: 9px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #1976D2;
            }}
            QPushButton:pressed {{
                background-color: #0D47A1;
            }}
            QPushButton:disabled {{
                background-color: {Config.COLORS['border']};
                color: #666666;
            }}
        """

        for button in [self.confirm_target_button, self.reset_button, self.manual_button, self.theme_button]:
            button.setStyleSheet(button_style)

        # 更新其他控件样式
        self.target_spinbox.setStyleSheet(f"""
            QSpinBox {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
                border: 1px solid {Config.COLORS['border']};
                border-radius: 3px;
                padding: 2px;
                font-size: 10px;
            }}
        """)

    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(self, "确认退出", "确定要退出T-1节拍器吗？")
        if reply == QMessageBox.StandardButton.Yes:
            # 停止所有定时器
            if hasattr(self.data_manager, 'ui_update_timer'):
                self.data_manager.ui_update_timer.stop()
            if hasattr(self.data_manager, 'cycle_timer'):
                self.data_manager.cycle_timer.stop()
            event.accept()
        else:
            event.ignore()
