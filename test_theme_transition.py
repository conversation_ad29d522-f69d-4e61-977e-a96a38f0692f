#!/usr/bin/env python3
"""
T-1节拍器主题过渡效果测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_theme_colors():
    """测试主题颜色配置"""
    print("🎨 测试主题颜色配置")
    print("-" * 40)
    
    from core.config import ThemeConfig
    
    # 测试所有主题
    themes = ThemeConfig.ALL_THEMES
    print(f"主题数量: {len(themes)}")
    
    for theme in themes:
        print(f"\n主题: {theme['name']}")
        print(f"  背景色: {theme['background']}")
        print(f"  主色调: {theme['primary']}")
        print(f"  主文字: {theme['text_primary']}")
        print(f"  次文字: {theme['text_secondary']}")
        
        # 检查必要的颜色字段
        required_fields = [
            'background', 'surface', 'card_bg', 'primary', 'primary_light',
            'success', 'warning', 'danger', 'text_primary', 'text_secondary',
            'text_on_primary', 'text_on_surface', 'border', 'border_light'
        ]
        
        for field in required_fields:
            assert field in theme, f"主题 {theme['name']} 缺少字段: {field}"
    
    print("✓ 所有主题颜色配置完整")
    return True

def test_theme_manager_transition():
    """测试主题管理器过渡效果"""
    print("\n🔄 测试主题管理器过渡效果")
    print("-" * 40)
    
    from core.theme_manager import ThemeManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    theme_manager = ThemeManager()
    
    # 测试过渡状态
    assert theme_manager.is_transitioning == False
    print("✓ 初始过渡状态正确")
    
    # 测试主题切换
    original_theme = theme_manager.get_current_theme()['name']
    print(f"当前主题: {original_theme}")
    
    # 切换到下一个主题
    theme_manager.switch_to_next_theme()
    
    # 检查过渡状态
    assert theme_manager.is_transitioning == True
    print("✓ 过渡状态已激活")
    
    # 等待过渡完成
    time.sleep(0.5)
    app.processEvents()
    
    new_theme = theme_manager.get_current_theme()['name']
    print(f"新主题: {new_theme}")
    
    assert original_theme != new_theme
    print("✓ 主题切换成功")
    
    return True

def test_fade_transition():
    """测试淡入淡出效果"""
    print("\n✨ 测试淡入淡出效果")
    print("-" * 40)
    
    from ui.fade_transition import FadeTransition
    from PyQt6.QtWidgets import QApplication, QLabel
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建测试组件
    test_widget = QLabel("测试组件")
    fade_transition = FadeTransition(test_widget)
    
    # 测试初始透明度
    initial_opacity = fade_transition.get_opacity()
    assert initial_opacity == 1.0
    print(f"✓ 初始透明度: {initial_opacity}")
    
    # 测试淡出
    fade_transition.fade_out()
    time.sleep(0.1)
    app.processEvents()
    
    # 测试设置透明度
    fade_transition.set_opacity(0.5)
    assert fade_transition.get_opacity() == 0.5
    print("✓ 透明度设置功能正常")
    
    # 测试淡入
    fade_transition.fade_in()
    time.sleep(0.1)
    app.processEvents()
    
    print("✓ 淡入淡出效果正常")
    
    return True

def test_ui_theme_integration():
    """测试UI主题集成"""
    print("\n🖼️ 测试UI主题集成")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 检查淡入淡出效果
    assert hasattr(main_window, 'fade_transition')
    print("✓ 主窗口淡入淡出效果已集成")
    
    # 检查对比组件
    comparison_widget = main_window.comparison_widget
    assert hasattr(comparison_widget, 'fade_transition')
    print("✓ 对比组件淡入淡出效果已集成")
    
    # 测试主题切换
    original_theme = main_window.theme_manager.get_current_theme()['name']
    main_window.on_theme_switch()
    
    time.sleep(0.1)
    app.processEvents()
    
    new_theme = main_window.theme_manager.get_current_theme()['name']
    print(f"主题切换: {original_theme} → {new_theme}")
    
    print("✓ UI主题集成正常")
    
    return True

def test_color_contrast():
    """测试颜色对比度"""
    print("\n🔍 测试颜色对比度")
    print("-" * 40)
    
    from core.config import ThemeConfig
    
    def hex_to_rgb(hex_color):
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    
    def calculate_luminance(rgb):
        """计算颜色亮度"""
        r, g, b = [x/255.0 for x in rgb]
        r = r/12.92 if r <= 0.03928 else ((r+0.055)/1.055)**2.4
        g = g/12.92 if g <= 0.03928 else ((g+0.055)/1.055)**2.4
        b = b/12.92 if b <= 0.03928 else ((b+0.055)/1.055)**2.4
        return 0.2126*r + 0.7152*g + 0.0722*b
    
    def contrast_ratio(color1, color2):
        """计算对比度"""
        lum1 = calculate_luminance(hex_to_rgb(color1))
        lum2 = calculate_luminance(hex_to_rgb(color2))
        return (max(lum1, lum2) + 0.05) / (min(lum1, lum2) + 0.05)
    
    # 测试所有主题的对比度
    for theme in ThemeConfig.ALL_THEMES:
        print(f"\n主题: {theme['name']}")
        
        # 测试文字与背景的对比度
        bg_text_contrast = contrast_ratio(theme['background'], theme['text_primary'])
        print(f"  背景-主文字对比度: {bg_text_contrast:.2f}")
        
        # 测试按钮文字与按钮背景的对比度
        btn_text_contrast = contrast_ratio(theme['primary'], theme['text_on_primary'])
        print(f"  按钮-按钮文字对比度: {btn_text_contrast:.2f}")
        
        # WCAG AA标准要求对比度至少为4.5:1
        assert bg_text_contrast >= 3.0, f"背景文字对比度过低: {bg_text_contrast:.2f}"
        assert btn_text_contrast >= 3.0, f"按钮文字对比度过低: {btn_text_contrast:.2f}"
    
    print("✓ 所有主题颜色对比度符合要求")
    
    return True

def test_theme_completeness():
    """测试主题完整性"""
    print("\n📋 测试主题完整性")
    print("-" * 40)
    
    from core.config import ThemeConfig
    
    # 检查主题数量
    assert len(ThemeConfig.ALL_THEMES) == 6
    print(f"✓ 主题数量正确: {len(ThemeConfig.ALL_THEMES)}")
    
    # 检查主题名称
    theme_names = [theme['name'] for theme in ThemeConfig.ALL_THEMES]
    expected_names = ['深色主题', '浅色主题', '高对比度', '蓝色海洋', '绿色森林', '紫色梦幻']
    
    for name in expected_names:
        assert name in theme_names, f"缺少主题: {name}"
    
    print("✓ 所有预期主题都存在")
    
    # 检查主题获取功能
    for theme_name in expected_names:
        theme = ThemeConfig.get_theme_by_name(theme_name)
        assert theme is not None, f"无法获取主题: {theme_name}"
        assert theme['name'] == theme_name
    
    print("✓ 主题获取功能正常")
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("🎨 T-1节拍器主题过渡效果测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 6
    
    try:
        # 测试主题颜色配置
        if test_theme_colors():
            success_count += 1
        
        # 测试主题管理器过渡
        if test_theme_manager_transition():
            success_count += 1
        
        # 测试淡入淡出效果
        if test_fade_transition():
            success_count += 1
        
        # 测试UI主题集成
        if test_ui_theme_integration():
            success_count += 1
        
        # 测试颜色对比度
        if test_color_contrast():
            success_count += 1
        
        # 测试主题完整性
        if test_theme_completeness():
            success_count += 1
        
        print("\n" + "=" * 50)
        print(f"测试结果: {success_count}/{total_tests} 通过")
        
        if success_count == total_tests:
            print("✅ 所有主题过渡效果测试通过！")
            print("\n🎉 新功能总结:")
            print("• 6种精美主题配色方案")
            print("• 淡入淡出过渡特效")
            print("• 完整的控件颜色适配")
            print("• 优化的字体颜色对比度")
            print("• 智能的主题切换机制")
            print("• 所有UI组件主题同步")
            print("\n🎨 主题列表:")
            print("1. 深色主题 - 经典深色护眼")
            print("2. 浅色主题 - 明亮清新白天")
            print("3. 高对比度 - 黑白分明清晰")
            print("4. 蓝色海洋 - 专业稳重商务")
            print("5. 绿色森林 - 自然清新环保")
            print("6. 紫色梦幻 - 优雅神秘浪漫")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
