"""
T-1节拍器自定义环形进度条组件
"""

import math
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import QPropertyAnimation, QEasingCurve, pyqtProperty, QTimer, QRectF
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QLinearGradient, QConicalGradient
from PyQt6.QtCore import Qt
from core.config import Config

class ProgressRing(QWidget):
    """自定义环形进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 进度值
        self._value = 0.0
        self._target_value = 0.0
        
        # 外观配置
        self.outer_radius = 150
        self.inner_radius = 120
        self.ring_width = 30
        
        # 颜色配置
        self.background_color = QColor(Config.COLORS['border'])
        self.progress_colors = [QColor(Config.COLORS['danger']), QColor(Config.COLORS['warning'])]
        
        # 动画
        self.animation = QPropertyAnimation(self, b"value")
        self.animation.setDuration(Config.ANIMATION['duration'])
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 粒子效果
        self.particles = []
        self.particle_timer = QTimer()
        self.particle_timer.timeout.connect(self._update_particles)
        self.particle_timer.start(50)  # 20 FPS
        
        # 光晕效果
        self.glow_intensity = 0.0
        self.glow_timer = QTimer()
        self.glow_timer.timeout.connect(self._update_glow)
        self.glow_timer.start(100)  # 10 FPS
        
        self.setMinimumSize(300, 300)
    
    @pyqtProperty(float)
    def value(self):
        return self._value
    
    @value.setter
    def value(self, val):
        self._value = max(0.0, min(100.0, val))
        self.update()
    
    def set_colors(self, colors: list):
        """设置进度条颜色"""
        self.progress_colors = [QColor(color) for color in colors]
        self.update()
    
    def animate_to(self, target_value: float):
        """动画到目标值"""
        self._target_value = max(0.0, min(100.0, target_value))
        
        self.animation.setStartValue(self._value)
        self.animation.setEndValue(self._target_value)
        self.animation.start()
        
        # 如果达到100%，触发粒子效果
        if self._target_value >= 100.0:
            self._trigger_celebration_particles()
    
    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 计算绘制区域
        rect = self.rect()
        center_x = rect.width() // 2
        center_y = rect.height() // 2
        
        # 绘制背景环
        self._draw_background_ring(painter, center_x, center_y)
        
        # 绘制进度环
        self._draw_progress_ring(painter, center_x, center_y)
        
        # 绘制粒子效果
        self._draw_particles(painter)
        
        # 绘制光晕效果
        if self.glow_intensity > 0:
            self._draw_glow(painter, center_x, center_y)
    
    def _draw_background_ring(self, painter: QPainter, center_x: int, center_y: int):
        """绘制背景环"""
        painter.setPen(QPen(self.background_color, self.ring_width, Qt.PenStyle.SolidLine))
        painter.setBrush(Qt.BrushStyle.NoBrush)
        
        # 绘制虚线背景环
        pen = QPen(self.background_color, self.ring_width // 3, Qt.PenStyle.DashLine)
        painter.setPen(pen)
        
        radius = (self.outer_radius + self.inner_radius) // 2
        painter.drawEllipse(
            center_x - radius, center_y - radius,
            radius * 2, radius * 2
        )
    
    def _draw_progress_ring(self, painter: QPainter, center_x: int, center_y: int):
        """绘制进度环"""
        if self._value <= 0:
            return
        
        # 创建渐变色
        gradient = self._create_gradient()
        
        # 设置画笔
        pen = QPen(QBrush(gradient), self.ring_width, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap)
        painter.setPen(pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        
        # 计算角度 (从顶部开始，顺时针)
        start_angle = -90 * 16  # Qt使用1/16度为单位
        span_angle = int((self._value / 100.0) * 360 * 16)
        
        radius = (self.outer_radius + self.inner_radius) // 2
        rect = QRectF(
            center_x - radius, center_y - radius,
            radius * 2, radius * 2
        )
        
        painter.drawArc(rect, start_angle, span_angle)
    
    def _create_gradient(self) -> QConicalGradient:
        """创建圆锥渐变"""
        gradient = QConicalGradient(0, 0, -90)  # 从顶部开始
        
        if len(self.progress_colors) >= 2:
            gradient.setColorAt(0.0, self.progress_colors[0])
            gradient.setColorAt(1.0, self.progress_colors[1])
        else:
            color = self.progress_colors[0] if self.progress_colors else QColor(Config.COLORS['primary'])
            gradient.setColorAt(0.0, color)
            gradient.setColorAt(1.0, color)
        
        return gradient
    
    def _draw_particles(self, painter: QPainter):
        """绘制粒子效果"""
        for particle in self.particles:
            painter.setPen(Qt.PenStyle.NoPen)
            painter.setBrush(QBrush(particle['color']))
            
            size = particle['size']
            painter.drawEllipse(
                int(particle['x'] - size/2), 
                int(particle['y'] - size/2),
                int(size), int(size)
            )
    
    def _draw_glow(self, painter: QPainter, center_x: int, center_y: int):
        """绘制光晕效果"""
        if self.glow_intensity <= 0:
            return
        
        # 创建径向渐变
        gradient = QLinearGradient()
        glow_color = QColor(self.progress_colors[0] if self.progress_colors else QColor(Config.COLORS['primary']))
        glow_color.setAlphaF(self.glow_intensity * 0.3)
        
        pen = QPen(glow_color, self.ring_width + 10, Qt.PenStyle.SolidLine)
        painter.setPen(pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        
        radius = (self.outer_radius + self.inner_radius) // 2
        painter.drawEllipse(
            center_x - radius, center_y - radius,
            radius * 2, radius * 2
        )
    
    def _trigger_celebration_particles(self):
        """触发庆祝粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        radius = (self.outer_radius + self.inner_radius) // 2
        
        # 创建爆炸粒子
        for i in range(20):
            angle = (i / 20) * 2 * math.pi
            speed = 5 + (i % 3) * 2
            
            particle = {
                'x': center_x + radius * math.cos(angle),
                'y': center_y + radius * math.sin(angle),
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': 8,
                'life': 1.0,
                'color': QColor(self.progress_colors[0] if self.progress_colors else QColor(Config.COLORS['success']))
            }
            self.particles.append(particle)
    
    def _update_particles(self):
        """更新粒子状态"""
        for particle in self.particles[:]:  # 复制列表以安全删除
            # 更新位置
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            
            # 更新生命值
            particle['life'] -= 0.05
            particle['size'] *= 0.98
            
            # 更新颜色透明度
            color = particle['color']
            color.setAlphaF(particle['life'])
            
            # 移除死亡粒子
            if particle['life'] <= 0 or particle['size'] < 1:
                self.particles.remove(particle)
        
        if self.particles:
            self.update()
    
    def _update_glow(self):
        """更新光晕效果"""
        # 简单的呼吸效果
        import time
        self.glow_intensity = (math.sin(time.time() * 3) + 1) / 2 * 0.5
        
        if self._value >= 90:  # 接近完成时增强光晕
            self.glow_intensity *= 2
        
        self.update()
