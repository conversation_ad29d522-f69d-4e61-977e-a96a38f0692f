#!/usr/bin/env python3
"""
T-1节拍器自动启动功能测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_auto_start():
    """测试自动启动功能"""
    print("🚀 测试自动启动功能")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建数据管理器（应该自动启动）
    data_manager = ProductionDataManager()
    
    # 检查自动启动状态
    assert data_manager.is_running == True
    print("✓ 数据管理器自动启动成功")
    
    # 检查定时器状态
    assert data_manager.ui_update_timer.isActive()
    assert data_manager.cycle_timer.isActive()
    print("✓ 定时器自动启动成功")
    
    # 检查周期开始时间
    assert data_manager.cycle_start_time is not None
    print("✓ 周期开始时间已设置")
    
    return True

def test_no_simulation():
    """测试无模拟数据生成"""
    print("\n🛑 测试无模拟数据生成")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 记录初始产量
    initial_count = data_manager.current_count
    print(f"初始产量: {initial_count}")
    
    # 等待一段时间，检查是否自动增加
    time.sleep(2)
    app.processEvents()
    
    # 产量应该保持不变（没有模拟数据）
    final_count = data_manager.current_count
    print(f"2秒后产量: {final_count}")
    
    assert final_count == initial_count
    print("✓ 无模拟数据自动生成，产量保持不变")
    
    return True

def test_manual_count_only():
    """测试只有手动计数功能"""
    print("\n👆 测试手动计数功能")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 记录初始产量
    initial_count = data_manager.current_count
    print(f"初始产量: {initial_count}")
    
    # 手动添加产量
    data_manager.add_manual_count(1)
    assert data_manager.current_count == initial_count + 1
    print(f"手动+1后产量: {data_manager.current_count}")
    
    # 再次手动添加
    data_manager.add_manual_count(3)
    assert data_manager.current_count == initial_count + 4
    print(f"手动+3后产量: {data_manager.current_count}")
    
    print("✓ 手动计数功能正常")
    
    return True

def test_reset_functionality():
    """测试重置功能"""
    print("\n🔄 测试重置功能")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 添加一些产量
    data_manager.add_manual_count(5)
    assert data_manager.current_count == 5
    print(f"添加产量后: {data_manager.current_count}")
    
    # 重置周期
    data_manager.restart_cycle()
    assert data_manager.current_count == 0
    assert data_manager.last_milestone == 0
    print(f"重置后产量: {data_manager.current_count}")
    
    print("✓ 重置功能正常")
    
    return True

def test_ui_auto_start():
    """测试UI自动启动"""
    print("\n🖼️ 测试UI自动启动")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 检查数据管理器状态
    assert main_window.data_manager.is_running == True
    print("✓ 主窗口数据管理器自动启动")
    
    # 检查按钮状态
    assert hasattr(main_window, 'reset_button')
    assert main_window.reset_button.text() == "重置"
    print("✓ 重置按钮存在")
    
    # 检查是否没有开始/停止按钮
    assert not hasattr(main_window, 'start_stop_button')
    print("✓ 开始/停止按钮已移除")
    
    # 检查确定按钮
    assert hasattr(main_window, 'confirm_target_button')
    assert main_window.confirm_target_button.text() == "确定"
    print("✓ 确定按钮存在")
    
    return True

def test_time_display():
    """测试时间显示"""
    print("\n⏰ 测试时间显示")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 测试时间格式化
    formatted_time = data_manager.get_time_formatted()
    current_time = data_manager.get_current_time_formatted()
    
    print(f"周期剩余时间: {formatted_time}")
    print(f"当前系统时间: {current_time}")
    
    assert ":" in formatted_time
    assert ":" in current_time
    print("✓ 时间显示格式正确")
    
    return True

def test_seconds_per_item_calculation():
    """测试秒/件计算"""
    print("\n📊 测试秒/件计算")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    from datetime import datetime, timedelta
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 模拟开始时间和产量
    data_manager.cycle_start_time = datetime.now() - timedelta(seconds=100)
    data_manager.current_count = 5
    
    # 计算秒/件
    seconds_per_item = data_manager.get_seconds_per_item()
    expected = 100 / 5  # 20秒/件
    
    print(f"耗时: 100秒")
    print(f"产量: 5件")
    print(f"计算结果: {seconds_per_item:.1f}秒/件")
    print(f"期望结果: {expected:.1f}秒/件")
    
    assert abs(seconds_per_item - expected) < 0.1
    print("✓ 秒/件计算正确")
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 T-1节拍器自动启动功能测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 6
    
    try:
        # 测试自动启动
        if test_auto_start():
            success_count += 1
        
        # 测试无模拟数据
        if test_no_simulation():
            success_count += 1
        
        # 测试手动计数
        if test_manual_count_only():
            success_count += 1
        
        # 测试重置功能
        if test_reset_functionality():
            success_count += 1
        
        # 测试UI自动启动
        if test_ui_auto_start():
            success_count += 1
        
        # 测试时间显示
        if test_time_display():
            success_count += 1
        
        # 测试秒/件计算
        test_seconds_per_item_calculation()
        
        print("\n" + "=" * 50)
        print(f"测试结果: {success_count}/{total_tests} 通过")
        
        if success_count == total_tests:
            print("✅ 所有自动启动功能测试通过！")
            print("\n🎉 改进功能总结:")
            print("• 程序启动后自动开始倒计时")
            print("• 移除开始按钮，简化操作")
            print("• 只保留手动计数，无自动模拟")
            print("• 添加重置按钮重新开始周期")
            print("• 保持目标设置确定按钮机制")
            print("• 保持所有主题和特效功能")
            print("\n🚀 使用方法:")
            print("1. 运行程序自动开始倒计时")
            print("2. 设置目标产量并点击确定")
            print("3. 使用+1按钮或COM8数据增加产量")
            print("4. 使用重置按钮重新开始周期")
            print("5. 使用🎨按钮切换主题")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
