"""
T-1节拍器核心对比显示组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QProgressBar, QFrame)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor, QPainter, QPen, QBrush
from core.config import Config
from core.color_theme import ColorThemeManager
from ui.progress_ring import ProgressRing
from ui.celebration import CelebrationWidget

class ComparisonWidget(QWidget):
    """产量对比显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.color_manager = ColorThemeManager()
        
        # 数据
        self.current_count = 0
        self.target_count = Config.DEFAULT_TARGET
        self.time_remaining = Config.COUNTDOWN_DURATION
        
        # 动画属性
        self._number_scale = 1.0
        self._symbol_rotation = 0.0
        
        self.setup_ui()
        self.setup_animations()
        self.apply_theme()
    
    def setup_ui(self):
        """设置UI布局"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(40, 40, 40, 40)
        
        # 标题区域
        self.create_title_section(layout)
        
        # 主要对比区域
        self.create_main_comparison_section(layout)
        
        # 进度环区域
        self.create_progress_section(layout)
        
        # 底部信息区域
        self.create_info_section(layout)
        
        # 庆祝动画层
        self.celebration_widget = CelebrationWidget(self)
        
    def create_title_section(self, layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.Shape.Box)
        title_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Config.COLORS['background']};
                border: 2px solid {Config.COLORS['border']};
                border-radius: 10px;
                padding: 10px;
            }}
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        # 标题
        self.title_label = QLabel("T-1节拍器 - 产量对比仪表盘")
        font = QFont(*Config.FONTS['hint_text'])
        font.setPointSize(28)
        self.title_label.setFont(font)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        # 倒计时
        self.timer_label = QLabel("10:00")
        timer_font = QFont(*Config.FONTS['timer_text'])
        self.timer_label.setFont(timer_font)
        self.timer_label.setStyleSheet(f"color: {Config.COLORS['warning']};")
        
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.timer_label)
        
        layout.addWidget(title_frame)
    
    def create_main_comparison_section(self, layout):
        """创建主要对比区域"""
        comparison_frame = QFrame()
        comparison_frame.setMinimumHeight(200)
        comparison_layout = QHBoxLayout(comparison_frame)
        comparison_layout.setSpacing(40)
        
        # 当前产量
        self.current_label = QLabel("0")
        current_font = QFont(*Config.FONTS['main_number'])
        self.current_label.setFont(current_font)
        self.current_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['primary']};
                background-color: rgba(33, 150, 243, 0.1);
                border: 3px solid {Config.COLORS['primary']};
                border-radius: 15px;
                padding: 20px;
            }}
        """)
        
        # VS符号
        self.vs_label = QLabel("→")
        vs_font = QFont("Arial Black", 48, QFont.Weight.Bold)
        self.vs_label.setFont(vs_font)
        self.vs_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.vs_label.setStyleSheet(f"color: {Config.COLORS['border']};")
        
        # 目标产量
        self.target_label = QLabel(str(self.target_count))
        target_font = QFont(*Config.FONTS['target_number'])
        self.target_label.setFont(target_font)
        self.target_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.target_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['warning']};
                background-color: rgba(255, 215, 0, 0.1);
                border: 3px solid {Config.COLORS['warning']};
                border-radius: 15px;
                padding: 20px;
            }}
        """)
        
        comparison_layout.addWidget(self.current_label, 2)
        comparison_layout.addWidget(self.vs_label, 1)
        comparison_layout.addWidget(self.target_label, 2)
        
        layout.addWidget(comparison_frame)
    
    def create_progress_section(self, layout):
        """创建进度环区域"""
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 环形进度条
        self.progress_ring = ProgressRing()
        progress_layout.addWidget(self.progress_ring, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 状态提示
        self.status_label = QLabel("准备开始...")
        status_font = QFont(*Config.FONTS['hint_text'])
        self.status_label.setFont(status_font)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_frame)
    
    def create_info_section(self, layout):
        """创建底部信息区域"""
        info_frame = QFrame()
        info_layout = QHBoxLayout(info_frame)
        
        # 进度百分比
        self.progress_label = QLabel("0%")
        progress_font = QFont(*Config.FONTS['hint_text'])
        self.progress_label.setFont(progress_font)
        self.progress_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        # 生产速率
        self.rate_label = QLabel("速率: 0 件/分钟")
        self.rate_label.setFont(progress_font)
        self.rate_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        # 预估完成
        self.estimate_label = QLabel("预估: 0 件")
        self.estimate_label.setFont(progress_font)
        self.estimate_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        info_layout.addWidget(self.progress_label)
        info_layout.addStretch()
        info_layout.addWidget(self.rate_label)
        info_layout.addStretch()
        info_layout.addWidget(self.estimate_label)
        
        layout.addWidget(info_frame)
    
    def setup_animations(self):
        """设置动画"""
        # 数字弹跳动画
        self.number_animation = QPropertyAnimation(self, b"numberScale")
        self.number_animation.setDuration(300)
        self.number_animation.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        # 符号旋转动画
        self.symbol_animation = QPropertyAnimation(self, b"symbolRotation")
        self.symbol_animation.setDuration(500)
        self.symbol_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 脉冲定时器
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self._update_pulse)
    
    @pyqtProperty(float)
    def numberScale(self):
        return self._number_scale
    
    @numberScale.setter
    def numberScale(self, value):
        self._number_scale = value
        self._apply_number_scale()
    
    @pyqtProperty(float)
    def symbolRotation(self):
        return self._symbol_rotation
    
    @symbolRotation.setter
    def symbolRotation(self, value):
        self._symbol_rotation = value
        # 这里可以添加符号旋转效果的实现
    
    def update_data(self, current: int, target: int, time_remaining: int):
        """更新显示数据"""
        old_current = self.current_count
        self.current_count = current
        self.target_count = target
        self.time_remaining = time_remaining
        
        # 更新显示
        self._update_numbers()
        self._update_progress()
        self._update_timer()
        self._update_colors()
        self._update_info()
        
        # 触发动画
        if current > old_current:
            self._trigger_number_bounce()
        
        # 检查里程碑和状态
        self._check_milestones()
        self._check_sprint_mode()
    
    def _update_numbers(self):
        """更新数字显示"""
        self.current_label.setText(str(self.current_count))
        self.target_label.setText(str(self.target_count))
    
    def _update_progress(self):
        """更新进度显示"""
        if self.target_count > 0:
            progress = min(100, (self.current_count / self.target_count) * 100)
        else:
            progress = 0
        
        # 更新进度环
        self.progress_ring.animate_to(progress)
        
        # 更新进度标签
        self.progress_label.setText(f"{progress:.1f}%")
    
    def _update_timer(self):
        """更新倒计时显示"""
        minutes = self.time_remaining // 60
        seconds = self.time_remaining % 60
        self.timer_label.setText(f"{minutes:02d}:{seconds:02d}")
        
        # 最后1分钟闪烁
        if self.time_remaining <= 60:
            if not self.pulse_timer.isActive():
                self.pulse_timer.start(500)
        else:
            self.pulse_timer.stop()
    
    def _update_colors(self):
        """更新颜色主题"""
        text_color, bg_color, hint_text = self.color_manager.get_color_theme(
            self.current_count, self.target_count
        )
        
        # 更新状态提示
        self.status_label.setText(hint_text)
        self.status_label.setStyleSheet(f"color: {text_color};")
        
        # 更新VS符号颜色
        symbol_color = self.color_manager.get_symbol_color(
            self.current_count, self.target_count
        )
        self.vs_label.setStyleSheet(f"color: {symbol_color};")
        
        # 更新进度环颜色
        progress_colors = self.color_manager.get_ring_gradient(
            (self.current_count / self.target_count) * 100 if self.target_count > 0 else 0
        )
        self.progress_ring.set_colors(progress_colors)
    
    def _update_info(self):
        """更新信息显示"""
        # 计算生产速率
        elapsed = Config.COUNTDOWN_DURATION - self.time_remaining
        if elapsed > 0:
            rate = (self.current_count / elapsed) * 60  # 件/分钟
            self.rate_label.setText(f"速率: {rate:.1f} 件/分钟")
            
            # 预估最终产量
            if self.time_remaining > 0:
                estimated = self.current_count + (rate / 60) * self.time_remaining
                self.estimate_label.setText(f"预估: {int(estimated)} 件")
            else:
                self.estimate_label.setText(f"最终: {self.current_count} 件")
        else:
            self.rate_label.setText("速率: 0 件/分钟")
            self.estimate_label.setText("预估: 0 件")
    
    def _trigger_number_bounce(self):
        """触发数字弹跳动画"""
        self.number_animation.setStartValue(1.0)
        self.number_animation.setEndValue(1.2)
        self.number_animation.finished.connect(self._reset_number_scale)
        self.number_animation.start()
    
    def _reset_number_scale(self):
        """重置数字缩放"""
        self.number_animation.setStartValue(1.2)
        self.number_animation.setEndValue(1.0)
        self.number_animation.finished.disconnect()
        self.number_animation.start()
    
    def _apply_number_scale(self):
        """应用数字缩放效果"""
        # 这里可以实现具体的缩放效果
        pass
    
    def _check_milestones(self):
        """检查里程碑"""
        if self.target_count > 0:
            progress = (self.current_count / self.target_count) * 100
            
            # 检查是否达成目标
            if self.current_count >= self.target_count:
                self.celebration_widget.celebrate_success()
            # 检查里程碑 (每20%一个里程碑)
            elif progress >= 20 and progress % 20 < 1:
                self.celebration_widget.celebrate_milestone(progress)
    
    def _check_sprint_mode(self):
        """检查冲刺模式"""
        if self.color_manager.is_sprint_mode(
            self.current_count, self.target_count, self.time_remaining
        ):
            self.celebration_widget.celebrate_sprint_mode()
    
    def _update_pulse(self):
        """更新脉冲效果"""
        # 倒计时最后1分钟的脉冲效果
        current_style = self.timer_label.styleSheet()
        if "background-color" in current_style:
            # 移除背景色
            self.timer_label.setStyleSheet(f"color: {Config.COLORS['warning']};")
        else:
            # 添加背景色
            self.timer_label.setStyleSheet(f"""
                color: {Config.COLORS['text_dark']};
                background-color: {Config.COLORS['warning']};
                border-radius: 5px;
                padding: 5px;
            """)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
            }}
        """)
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 调整庆祝组件大小
        self.celebration_widget.resize(self.size())
