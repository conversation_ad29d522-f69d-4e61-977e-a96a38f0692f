"""
T-1节拍器核心对比显示组件
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QProgressBar, QFrame)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, pyqtProperty, QTimer
from PyQt6.QtGui import QFont, QPalette, QColor, QPainter, QPen, QBrush
from core.config import Config
from core.color_theme import ColorThemeManager
from core.theme_manager import ThemeManager
from ui.celebration import CelebrationWidget

class ComparisonWidget(QWidget):
    """产量对比显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        self.color_manager = ColorThemeManager()
        self.theme_manager = ThemeManager()

        # 数据
        self.current_count = 0
        self.target_count = Config.DEFAULT_TARGET
        self.time_remaining = 600

        # 动画属性
        self._number_scale = 1.0
        self._symbol_rotation = 0.0

        self.setup_ui()
        self.setup_animations()
        self.apply_theme()

        # 连接主题变更信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """设置UI布局 - 紧凑版"""
        layout = QVBoxLayout(self)
        layout.setSpacing(5)
        layout.setContentsMargins(5, 5, 5, 5)

        # 主要对比区域 - 占主要空间
        self.create_compact_comparison_section(layout)

        # 简化信息区域
        self.create_compact_info_section(layout)

        # 庆祝动画层
        self.celebration_widget = CelebrationWidget(self)

    def create_compact_comparison_section(self, layout):
        """创建紧凑对比区域"""
        comparison_frame = QFrame()
        comparison_frame.setMinimumHeight(180)
        comparison_layout = QVBoxLayout(comparison_frame)
        comparison_layout.setSpacing(5)

        # 倒计时显示
        self.timer_label = QLabel("10:00")
        timer_font = QFont(*Config.FONTS['timer_text'])
        self.timer_label.setFont(timer_font)
        self.timer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.timer_label.setStyleSheet(f"color: {Config.COLORS['warning']}; margin: 2px;")
        comparison_layout.addWidget(self.timer_label)

        # 数字对比区域
        numbers_layout = QHBoxLayout()
        numbers_layout.setSpacing(10)

        # 当前产量
        self.current_label = QLabel("0")
        current_font = QFont(*Config.FONTS['main_number'])
        self.current_label.setFont(current_font)
        self.current_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['primary']};
                background-color: rgba(33, 150, 243, 0.1);
                border: 2px solid {Config.COLORS['primary']};
                border-radius: 8px;
                padding: 10px;
                min-height: 60px;
            }}
        """)

        # VS符号
        self.vs_label = QLabel("→")
        vs_font = QFont("Arial Black", 24, QFont.Weight.Bold)
        self.vs_label.setFont(vs_font)
        self.vs_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.vs_label.setStyleSheet(f"color: {Config.COLORS['border']}; margin: 5px;")
        self.vs_label.setMaximumWidth(30)

        # 目标产量
        self.target_label = QLabel(str(self.target_count))
        target_font = QFont(*Config.FONTS['target_number'])
        self.target_label.setFont(target_font)
        self.target_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.target_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['warning']};
                background-color: rgba(255, 215, 0, 0.1);
                border: 2px solid {Config.COLORS['warning']};
                border-radius: 8px;
                padding: 10px;
                min-height: 60px;
            }}
        """)

        numbers_layout.addWidget(self.current_label, 2)
        numbers_layout.addWidget(self.vs_label, 0)
        numbers_layout.addWidget(self.target_label, 2)

        comparison_layout.addLayout(numbers_layout)

        # 状态提示
        self.status_label = QLabel("准备开始...")
        status_font = QFont(*Config.FONTS['hint_text'])
        self.status_label.setFont(status_font)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; margin: 2px;")
        comparison_layout.addWidget(self.status_label)

        layout.addWidget(comparison_frame)

    def create_compact_info_section(self, layout):
        """创建紧凑信息区域"""
        info_frame = QFrame()
        info_frame.setMaximumHeight(40)
        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(5, 2, 5, 2)
        info_layout.setSpacing(2)

        # 第一行：进度和速率
        first_row = QHBoxLayout()

        # 进度百分比
        self.progress_label = QLabel("0%")
        progress_font = QFont(*Config.FONTS['hint_text'])
        self.progress_label.setFont(progress_font)
        self.progress_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")

        # 生产速率（改为秒/件）
        self.rate_label = QLabel("0秒/件")
        self.rate_label.setFont(progress_font)
        self.rate_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")

        first_row.addWidget(self.progress_label)
        first_row.addStretch()
        first_row.addWidget(self.rate_label)

        # 第二行：系统时间
        second_row = QHBoxLayout()

        self.system_time_label = QLabel("00:00:00")
        self.system_time_label.setFont(progress_font)
        self.system_time_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.system_time_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")

        second_row.addWidget(self.system_time_label)

        info_layout.addLayout(first_row)
        info_layout.addLayout(second_row)

        layout.addWidget(info_frame)

    def create_title_section(self, layout):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setFrameStyle(QFrame.Shape.Box)
        title_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Config.COLORS['background']};
                border: 2px solid {Config.COLORS['border']};
                border-radius: 10px;
                padding: 10px;
            }}
        """)
        
        title_layout = QHBoxLayout(title_frame)
        
        # 标题
        self.title_label = QLabel("T-1节拍器 - 产量对比仪表盘")
        font = QFont(*Config.FONTS['hint_text'])
        font.setPointSize(28)
        self.title_label.setFont(font)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        # 倒计时
        self.timer_label = QLabel("10:00")
        timer_font = QFont(*Config.FONTS['timer_text'])
        self.timer_label.setFont(timer_font)
        self.timer_label.setStyleSheet(f"color: {Config.COLORS['warning']};")
        
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        title_layout.addWidget(self.timer_label)
        
        layout.addWidget(title_frame)
    
    def create_main_comparison_section(self, layout):
        """创建主要对比区域"""
        comparison_frame = QFrame()
        comparison_frame.setMinimumHeight(200)
        comparison_layout = QHBoxLayout(comparison_frame)
        comparison_layout.setSpacing(40)
        
        # 当前产量
        self.current_label = QLabel("0")
        current_font = QFont(*Config.FONTS['main_number'])
        self.current_label.setFont(current_font)
        self.current_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['primary']};
                background-color: rgba(33, 150, 243, 0.1);
                border: 3px solid {Config.COLORS['primary']};
                border-radius: 15px;
                padding: 20px;
            }}
        """)
        
        # VS符号
        self.vs_label = QLabel("→")
        vs_font = QFont("Arial Black", 48, QFont.Weight.Bold)
        self.vs_label.setFont(vs_font)
        self.vs_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.vs_label.setStyleSheet(f"color: {Config.COLORS['border']};")
        
        # 目标产量
        self.target_label = QLabel(str(self.target_count))
        target_font = QFont(*Config.FONTS['target_number'])
        self.target_label.setFont(target_font)
        self.target_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.target_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['warning']};
                background-color: rgba(255, 215, 0, 0.1);
                border: 3px solid {Config.COLORS['warning']};
                border-radius: 15px;
                padding: 20px;
            }}
        """)
        
        comparison_layout.addWidget(self.current_label, 2)
        comparison_layout.addWidget(self.vs_label, 1)
        comparison_layout.addWidget(self.target_label, 2)
        
        layout.addWidget(comparison_frame)
    
    def create_progress_section(self, layout):
        """创建进度环区域"""
        progress_frame = QFrame()
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 环形进度条
        self.progress_ring = ProgressRing()
        progress_layout.addWidget(self.progress_ring, alignment=Qt.AlignmentFlag.AlignCenter)
        
        # 状态提示
        self.status_label = QLabel("准备开始...")
        status_font = QFont(*Config.FONTS['hint_text'])
        self.status_label.setFont(status_font)
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_frame)
    
    def create_info_section(self, layout):
        """创建底部信息区域"""
        info_frame = QFrame()
        info_layout = QHBoxLayout(info_frame)
        
        # 进度百分比
        self.progress_label = QLabel("0%")
        progress_font = QFont(*Config.FONTS['hint_text'])
        self.progress_label.setFont(progress_font)
        self.progress_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        # 生产速率
        self.rate_label = QLabel("速率: 0 件/分钟")
        self.rate_label.setFont(progress_font)
        self.rate_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        # 预估完成
        self.estimate_label = QLabel("预估: 0 件")
        self.estimate_label.setFont(progress_font)
        self.estimate_label.setStyleSheet(f"color: {Config.COLORS['text_light']};")
        
        info_layout.addWidget(self.progress_label)
        info_layout.addStretch()
        info_layout.addWidget(self.rate_label)
        info_layout.addStretch()
        info_layout.addWidget(self.estimate_label)
        
        layout.addWidget(info_frame)
    
    def setup_animations(self):
        """设置动画"""
        # 数字弹跳动画
        self.number_animation = QPropertyAnimation(self, b"numberScale")
        self.number_animation.setDuration(300)
        self.number_animation.setEasingCurve(QEasingCurve.Type.OutBounce)
        
        # 符号旋转动画
        self.symbol_animation = QPropertyAnimation(self, b"symbolRotation")
        self.symbol_animation.setDuration(500)
        self.symbol_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        # 脉冲定时器
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self._update_pulse)
    
    @pyqtProperty(float)
    def numberScale(self):
        return self._number_scale
    
    @numberScale.setter
    def numberScale(self, value):
        self._number_scale = value
        self._apply_number_scale()
    
    @pyqtProperty(float)
    def symbolRotation(self):
        return self._symbol_rotation
    
    @symbolRotation.setter
    def symbolRotation(self, value):
        self._symbol_rotation = value
        # 这里可以添加符号旋转效果的实现
    
    def update_data(self, current: int, target: int, time_remaining: int, data_manager=None):
        """更新显示数据"""
        old_current = self.current_count
        self.current_count = current
        self.target_count = target
        self.time_remaining = time_remaining

        # 更新显示
        self._update_numbers()
        self._update_progress()
        self._update_timer()
        self._update_colors()
        self._update_info(data_manager)

        # 触发动画
        if current > old_current:
            self._trigger_number_bounce()

        # 检查里程碑和状态
        self._check_milestones()
        self._check_sprint_mode()
    
    def _update_numbers(self):
        """更新数字显示"""
        self.current_label.setText(str(self.current_count))
        self.target_label.setText(str(self.target_count))
    
    def _update_progress(self):
        """更新进度显示"""
        if self.target_count > 0:
            progress = min(100, (self.current_count / self.target_count) * 100)
        else:
            progress = 0

        # 更新进度标签
        self.progress_label.setText(f"{progress:.1f}%")
    
    def _update_timer(self):
        """更新倒计时显示"""
        minutes = self.time_remaining // 60
        seconds = self.time_remaining % 60
        self.timer_label.setText(f"{minutes:02d}:{seconds:02d}")
        
        # 最后1分钟闪烁
        if self.time_remaining <= 60:
            if not self.pulse_timer.isActive():
                self.pulse_timer.start(500)
        else:
            self.pulse_timer.stop()
    
    def _update_colors(self):
        """更新颜色主题"""
        text_color, bg_color, hint_text = self.color_manager.get_color_theme(
            self.current_count, self.target_count
        )
        
        # 更新状态提示
        self.status_label.setText(hint_text)
        self.status_label.setStyleSheet(f"color: {text_color};")
        
        # 更新VS符号颜色
        symbol_color = self.color_manager.get_symbol_color(
            self.current_count, self.target_count
        )
        self.vs_label.setStyleSheet(f"color: {symbol_color};")
        
        # 更新背景颜色（可选）
        # 这里可以添加背景颜色变化的逻辑
    
    def _update_info(self, data_manager=None):
        """更新信息显示"""
        if data_manager:
            # 更新秒/件显示
            seconds_per_item = data_manager.get_seconds_per_item()
            if seconds_per_item > 0:
                self.rate_label.setText(f"{seconds_per_item:.1f}秒/件")
            else:
                self.rate_label.setText("0秒/件")

            # 更新系统时间
            current_time = data_manager.get_current_time_formatted()
            self.system_time_label.setText(current_time)
        else:
            self.rate_label.setText("0秒/件")
            self.system_time_label.setText("00:00:00")
    
    def _trigger_number_bounce(self):
        """触发数字弹跳动画"""
        self.number_animation.setStartValue(1.0)
        self.number_animation.setEndValue(1.2)
        self.number_animation.finished.connect(self._reset_number_scale)
        self.number_animation.start()
    
    def _reset_number_scale(self):
        """重置数字缩放"""
        self.number_animation.setStartValue(1.2)
        self.number_animation.setEndValue(1.0)
        self.number_animation.finished.disconnect()
        self.number_animation.start()
    
    def _apply_number_scale(self):
        """应用数字缩放效果"""
        # 这里可以实现具体的缩放效果
        pass
    
    def _check_milestones(self):
        """检查里程碑"""
        if self.target_count > 0:
            progress = (self.current_count / self.target_count) * 100
            
            # 检查是否达成目标
            if self.current_count >= self.target_count:
                self.celebration_widget.celebrate_success()
            # 检查里程碑 (每20%一个里程碑)
            elif progress >= 20 and progress % 20 < 1:
                self.celebration_widget.celebrate_milestone(progress)
    
    def _check_sprint_mode(self):
        """检查冲刺模式"""
        if self.color_manager.is_sprint_mode(
            self.current_count, self.target_count, self.time_remaining
        ):
            self.celebration_widget.celebrate_sprint_mode()
    
    def _update_pulse(self):
        """更新脉冲效果"""
        # 倒计时最后1分钟的脉冲效果
        current_style = self.timer_label.styleSheet()
        if "background-color" in current_style:
            # 移除背景色
            self.timer_label.setStyleSheet(f"color: {Config.COLORS['warning']};")
        else:
            # 添加背景色
            self.timer_label.setStyleSheet(f"""
                color: {Config.COLORS['text_dark']};
                background-color: {Config.COLORS['warning']};
                border-radius: 5px;
                padding: 5px;
            """)
    
    def apply_theme(self):
        """应用主题"""
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {Config.COLORS['background']};
                color: {Config.COLORS['text_light']};
            }}
        """)

    def on_theme_changed(self, theme):
        """主题变更回调"""
        self.apply_theme()
        # 更新所有组件的样式
        self._update_all_styles()

    def _update_all_styles(self):
        """更新所有组件样式"""
        # 更新倒计时样式
        self.timer_label.setStyleSheet(f"color: {Config.COLORS['warning']}; margin: 2px;")

        # 更新当前产量样式
        self.current_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['primary']};
                background-color: rgba(33, 150, 243, 0.1);
                border: 2px solid {Config.COLORS['primary']};
                border-radius: 8px;
                padding: 10px;
                min-height: 60px;
            }}
        """)

        # 更新VS符号样式
        self.vs_label.setStyleSheet(f"color: {Config.COLORS['border']}; margin: 5px;")

        # 更新目标产量样式
        self.target_label.setStyleSheet(f"""
            QLabel {{
                color: {Config.COLORS['warning']};
                background-color: rgba(255, 215, 0, 0.1);
                border: 2px solid {Config.COLORS['warning']};
                border-radius: 8px;
                padding: 10px;
                min-height: 60px;
            }}
        """)

        # 更新状态和信息标签样式
        self.status_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; margin: 2px;")
        self.progress_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")
        self.rate_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")
        self.system_time_label.setStyleSheet(f"color: {Config.COLORS['text_light']}; font-size: 10px;")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 调整庆祝组件大小
        self.celebration_widget.resize(self.size())
