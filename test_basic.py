#!/usr/bin/env python3
"""
T-1节拍器基本功能测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from core.config import Config
        print("✓ Config 导入成功")
        
        from core.color_theme import ColorThemeManager
        print("✓ ColorThemeManager 导入成功")
        
        from core.data_manager import ProductionDataManager
        print("✓ ProductionDataManager 导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_config():
    """测试配置模块"""
    print("\n测试配置模块...")
    
    from core.config import Config
    
    # 测试基本配置
    assert Config.WINDOW_WIDTH == 1200
    assert Config.WINDOW_HEIGHT == 800
    assert Config.DEFAULT_TARGET == 100
    print("✓ 基本配置正确")
    
    # 测试颜色配置
    assert 'success' in Config.COLORS
    assert 'warning' in Config.COLORS
    assert 'danger' in Config.COLORS
    print("✓ 颜色配置正确")
    
    # 测试字体配置
    assert 'main_number' in Config.FONTS
    assert 'target_number' in Config.FONTS
    print("✓ 字体配置正确")

def test_color_theme():
    """测试颜色主题系统"""
    print("\n测试颜色主题系统...")
    
    from core.color_theme import ColorThemeManager
    
    color_manager = ColorThemeManager()
    
    # 测试颜色主题获取
    text_color, bg_color, hint = color_manager.get_color_theme(50, 100)
    assert text_color is not None
    assert bg_color is not None
    assert hint is not None
    print("✓ 颜色主题获取正常")
    
    # 测试不同进度的颜色
    # 低进度 - 应该是红色
    text_color, bg_color, hint = color_manager.get_color_theme(30, 100)
    assert "加速" in hint
    print("✓ 低进度颜色正确")
    
    # 高进度 - 应该是金色
    text_color, bg_color, hint = color_manager.get_color_theme(85, 100)
    assert "接近" in hint
    print("✓ 高进度颜色正确")
    
    # 超额完成 - 应该是绿色
    text_color, bg_color, hint = color_manager.get_color_theme(120, 100)
    assert "超额" in hint
    print("✓ 超额完成颜色正确")
    
    # 测试渐变色
    gradient_color = color_manager.gradient_red_to_yellow(0.5)
    assert gradient_color.startswith('#')
    print("✓ 渐变色计算正常")

def test_data_manager():
    """测试数据管理器"""
    print("\n测试数据管理器...")
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    # 创建应用程序实例（QObject需要）
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 测试基本属性
    assert data_manager.current_count == 0
    assert data_manager.target == 100
    assert data_manager.time_remaining == 600
    print("✓ 初始状态正确")
    
    # 测试设置目标
    data_manager.set_target(150)
    assert data_manager.target == 150
    print("✓ 目标设置正常")
    
    # 测试手动添加
    data_manager.add_manual_count(5)
    assert data_manager.current_count == 5
    print("✓ 手动添加正常")
    
    # 测试进度计算
    progress = data_manager.get_progress_percentage()
    expected = (5 / 150) * 100
    assert abs(progress - expected) < 0.1
    print("✓ 进度计算正确")
    
    # 测试时间格式化
    time_str = data_manager.get_time_formatted()
    assert ":" in time_str
    print("✓ 时间格式化正常")

def test_ui_imports():
    """测试UI模块导入"""
    print("\n测试UI模块导入...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        if not QApplication.instance():
            app = QApplication([])
        
        from ui.progress_ring import ProgressRing
        print("✓ ProgressRing 导入成功")
        
        from ui.celebration import CelebrationWidget
        print("✓ CelebrationWidget 导入成功")
        
        from ui.comparison_widget import ComparisonWidget
        print("✓ ComparisonWidget 导入成功")
        
        from ui.main_window import MainWindow
        print("✓ MainWindow 导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ UI模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("T-1节拍器基本功能测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        return 1
    
    # 测试配置
    try:
        test_config()
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return 1
    
    # 测试颜色主题
    try:
        test_color_theme()
    except Exception as e:
        print(f"✗ 颜色主题测试失败: {e}")
        return 1
    
    # 测试数据管理器
    try:
        test_data_manager()
    except Exception as e:
        print(f"✗ 数据管理器测试失败: {e}")
        return 1
    
    # 测试UI导入
    if not test_ui_imports():
        return 1
    
    print("\n" + "=" * 50)
    print("✓ 所有基本功能测试通过！")
    print("T-1节拍器核心功能正常工作")
    print("=" * 50)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
