#!/usr/bin/env python3
"""
T-1节拍器功能改进测试
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_time_based_cycle():
    """测试基于系统时间的10分钟周期"""
    print("⏰ 测试基于系统时间的10分钟周期")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 测试周期信息获取
    time_remaining = data_manager.get_current_cycle_info()
    print(f"当前周期剩余时间: {time_remaining}秒")
    
    # 测试时间格式化
    formatted_time = data_manager.get_time_formatted()
    print(f"格式化时间: {formatted_time}")
    
    # 测试系统时间获取
    current_time = data_manager.get_current_time_formatted()
    print(f"当前系统时间: {current_time}")
    
    # 测试周期结束检测
    assert time_remaining >= 0
    assert ":" in formatted_time
    assert ":" in current_time
    
    print("✓ 基于系统时间的周期管理正常")
    return True

def test_seconds_per_item():
    """测试秒/件计算"""
    print("\n📊 测试秒/件计算")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    from datetime import datetime, timedelta
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 模拟开始时间
    data_manager.cycle_start_time = datetime.now() - timedelta(seconds=60)  # 1分钟前开始
    data_manager.current_count = 4  # 4件产品
    
    # 计算秒/件
    seconds_per_item = data_manager.get_seconds_per_item()
    expected = 60 / 4  # 15秒/件
    
    print(f"生产时间: 60秒")
    print(f"产品数量: 4件")
    print(f"计算结果: {seconds_per_item:.1f}秒/件")
    print(f"期望结果: {expected:.1f}秒/件")
    
    assert abs(seconds_per_item - expected) < 0.1
    print("✓ 秒/件计算正确")
    
    # 测试零产量情况
    data_manager.current_count = 0
    seconds_per_item = data_manager.get_seconds_per_item()
    assert seconds_per_item == 0.0
    print("✓ 零产量处理正确")
    
    return True

def test_theme_manager():
    """测试主题管理器"""
    print("\n🎨 测试主题管理器")
    print("-" * 40)
    
    from core.theme_manager import ThemeManager
    from core.config import ThemeConfig
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    theme_manager = ThemeManager()
    
    # 测试初始主题
    current_theme = theme_manager.get_current_theme()
    assert current_theme['name'] == '深色主题'
    print(f"✓ 初始主题: {current_theme['name']}")
    
    # 测试主题列表
    available_themes = theme_manager.get_available_themes()
    theme_names = theme_manager.get_theme_names()
    
    print(f"可用主题数量: {len(available_themes)}")
    print(f"主题名称: {theme_names}")
    
    assert len(available_themes) == 5
    assert '深色主题' in theme_names
    assert '浅色主题' in theme_names
    assert '高对比度' in theme_names
    assert '蓝色主题' in theme_names
    assert '绿色主题' in theme_names
    
    # 测试主题切换
    theme_manager.set_theme('浅色主题')
    current_theme = theme_manager.get_current_theme()
    assert current_theme['name'] == '浅色主题'
    print(f"✓ 主题切换成功: {current_theme['name']}")
    
    # 测试循环切换
    theme_manager.switch_to_next_theme()
    current_theme = theme_manager.get_current_theme()
    print(f"✓ 循环切换成功: {current_theme['name']}")
    
    return True

def test_stop_behavior():
    """测试停止按钮行为"""
    print("\n⏹️ 测试停止按钮行为")
    print("-" * 40)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    
    # 启动周期
    data_manager.start_cycle(50)
    assert data_manager.is_running == True
    print("✓ 周期启动成功")
    
    # 停止周期
    data_manager.stop_cycle()
    assert data_manager.is_running == False
    print("✓ 周期停止成功")
    
    # 验证数据保持
    assert data_manager.current_count >= 0
    assert data_manager.target == 50
    print("✓ 停止后数据保持正常")
    
    return True

def test_ui_integration():
    """测试UI集成"""
    print("\n🖼️ 测试UI集成")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 检查主题管理器
    assert hasattr(main_window, 'theme_manager')
    print("✓ 主题管理器集成成功")
    
    # 检查主题切换按钮
    assert hasattr(main_window, 'theme_button')
    assert main_window.theme_button.text() == "🎨"
    print("✓ 主题切换按钮存在")
    
    # 检查对比组件的新功能
    comparison_widget = main_window.comparison_widget
    assert hasattr(comparison_widget, 'system_time_label')
    assert hasattr(comparison_widget, 'rate_label')
    print("✓ 对比组件新功能存在")
    
    # 测试主题切换
    original_theme = main_window.theme_manager.get_current_theme()['name']
    main_window.on_theme_switch()
    new_theme = main_window.theme_manager.get_current_theme()['name']
    assert original_theme != new_theme
    print(f"✓ 主题切换功能正常: {original_theme} → {new_theme}")
    
    return True

def test_display_formats():
    """测试显示格式"""
    print("\n📱 测试显示格式")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.comparison_widget import ComparisonWidget
    from core.data_manager import ProductionDataManager
    from datetime import datetime, timedelta
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建组件
    widget = ComparisonWidget()
    data_manager = ProductionDataManager()
    
    # 模拟数据
    data_manager.cycle_start_time = datetime.now() - timedelta(seconds=120)
    data_manager.current_count = 8
    
    # 更新显示
    widget.update_data(8, 10, 480, data_manager)
    
    # 检查显示格式
    rate_text = widget.rate_label.text()
    time_text = widget.system_time_label.text()
    
    print(f"速率显示: {rate_text}")
    print(f"时间显示: {time_text}")
    
    assert "秒/件" in rate_text
    assert ":" in time_text
    print("✓ 显示格式正确")
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 T-1节拍器功能改进测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 6
    
    try:
        # 测试基于时间的周期
        if test_time_based_cycle():
            success_count += 1
        
        # 测试秒/件计算
        if test_seconds_per_item():
            success_count += 1
        
        # 测试主题管理器
        if test_theme_manager():
            success_count += 1
        
        # 测试停止行为
        if test_stop_behavior():
            success_count += 1
        
        # 测试UI集成
        if test_ui_integration():
            success_count += 1
        
        # 测试显示格式
        if test_display_formats():
            success_count += 1
        
        print("\n" + "=" * 50)
        print(f"测试结果: {success_count}/{total_tests} 通过")
        
        if success_count == total_tests:
            print("✅ 所有功能改进测试通过！")
            print("\n🎉 改进功能总结:")
            print("• 基于系统时间的10分钟周期管理")
            print("• 停止按钮不退出程序，只停止监控")
            print("• 秒/件格式的生产速度显示")
            print("• 5种配色主题可切换")
            print("• 底部显示系统时间，减少空白")
            print("• 保持360x360紧凑界面")
            print("• 保持烟花庆祝特效")
            print("• 保持确定按钮机制")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
