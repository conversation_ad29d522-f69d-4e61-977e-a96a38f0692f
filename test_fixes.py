#!/usr/bin/env python3
"""
T-1节拍器修复验证测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_window_size_fix():
    """测试窗口大小修复"""
    print("🖼️ 测试窗口大小修复")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 记录初始大小
    initial_size = main_window.size()
    print(f"初始窗口大小: {initial_size.width()}x{initial_size.height()}")
    
    # 切换主题
    main_window.on_theme_switch()
    time.sleep(0.1)
    app.processEvents()
    
    # 检查大小是否保持不变
    after_theme_size = main_window.size()
    print(f"主题切换后大小: {after_theme_size.width()}x{after_theme_size.height()}")
    
    assert initial_size == after_theme_size
    print("✓ 窗口大小在主题切换后保持不变")
    
    # 再次切换主题
    main_window.on_theme_switch()
    time.sleep(0.1)
    app.processEvents()
    
    final_size = main_window.size()
    print(f"再次切换后大小: {final_size.width()}x{final_size.height()}")
    
    assert initial_size == final_size
    print("✓ 多次主题切换后窗口大小保持稳定")
    
    return True

def test_enhanced_fireworks():
    """测试增强的烟花效果"""
    print("\n🎆 测试增强的烟花效果")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建庆祝组件
    celebration = CelebrationWidget()
    celebration.resize(360, 360)
    
    # 测试烟花启动
    celebration.celebrate_success()
    
    # 检查烟花状态
    assert celebration.is_celebrating == True
    assert celebration.continuous_fireworks == True
    print("✓ 烟花庆祝成功启动")
    
    # 检查定时器
    assert hasattr(celebration, 'fireworks_timer')
    assert hasattr(celebration, 'multi_fireworks_timer')
    assert celebration.fireworks_timer.isActive()
    assert celebration.multi_fireworks_timer.isActive()
    print("✓ 烟花定时器正常运行")
    
    # 等待一些粒子生成
    time.sleep(0.5)
    app.processEvents()
    
    # 检查粒子数量
    particle_count = len(celebration.particles)
    print(f"生成粒子数量: {particle_count}")
    assert particle_count > 0
    print("✓ 烟花粒子成功生成")
    
    # 检查粒子属性
    if celebration.particles:
        particle = celebration.particles[0]
        assert 'trail' in particle
        assert 'glow' in particle
        assert 'sparkle' in particle
        print("✓ 粒子增强属性存在")
    
    # 停止烟花
    celebration.stop_fireworks_celebration()
    assert celebration.continuous_fireworks == False
    print("✓ 烟花成功停止")
    
    return True

def test_painter_fix():
    """测试QPainter修复"""
    print("\n🎨 测试QPainter修复")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    from PyQt6.QtGui import QPaintEvent
    from PyQt6.QtCore import QRect
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建庆祝组件
    celebration = CelebrationWidget()
    celebration.resize(360, 360)
    celebration.show()
    
    # 启动庆祝
    celebration.celebrate_success()
    
    # 模拟绘制事件
    try:
        paint_event = QPaintEvent(QRect(0, 0, 360, 360))
        celebration.paintEvent(paint_event)
        print("✓ paintEvent执行成功，无QPainter错误")
    except Exception as e:
        print(f"❌ paintEvent执行失败: {e}")
        return False
    
    # 多次绘制测试
    for i in range(5):
        try:
            paint_event = QPaintEvent(QRect(0, 0, 360, 360))
            celebration.paintEvent(paint_event)
        except Exception as e:
            print(f"❌ 第{i+1}次绘制失败: {e}")
            return False
    
    print("✓ 多次绘制测试通过")
    
    celebration.stop_fireworks_celebration()
    return True

def test_theme_transition_stability():
    """测试主题过渡稳定性"""
    print("\n🔄 测试主题过渡稳定性")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()  # 显示窗口

    # 快速连续切换主题
    for i in range(6):
        main_window.on_theme_switch()
        time.sleep(0.1)
        app.processEvents()
        
        # 检查窗口状态
        assert main_window.isVisible()
        print(f"✓ 第{i+1}次主题切换成功")
    
    print("✓ 快速主题切换稳定性测试通过")
    
    return True

def test_fireworks_performance():
    """测试烟花性能"""
    print("\n⚡ 测试烟花性能")
    print("-" * 40)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    import time
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建庆祝组件
    celebration = CelebrationWidget()
    celebration.resize(360, 360)
    
    # 启动烟花
    celebration.celebrate_success()
    
    # 运行一段时间，测试性能
    start_time = time.time()
    frame_count = 0
    
    for _ in range(100):  # 模拟100帧
        app.processEvents()
        frame_count += 1
        time.sleep(0.016)  # 约60FPS
    
    end_time = time.time()
    duration = end_time - start_time
    fps = frame_count / duration
    
    print(f"运行时间: {duration:.2f}秒")
    print(f"平均FPS: {fps:.1f}")
    print(f"粒子数量: {len(celebration.particles)}")
    
    # 检查性能指标
    assert fps > 30  # 至少30FPS
    assert len(celebration.particles) < 500  # 粒子数量控制
    print("✓ 烟花性能测试通过")
    
    celebration.stop_fireworks_celebration()
    return True

def test_color_theme_completeness():
    """测试颜色主题完整性"""
    print("\n🎨 测试颜色主题完整性")
    print("-" * 40)
    
    from core.config import ThemeConfig, Config
    from core.theme_manager import ThemeManager
    from PyQt6.QtWidgets import QApplication
    
    if not QApplication.instance():
        app = QApplication([])
    
    theme_manager = ThemeManager()
    
    # 测试所有主题
    for theme in ThemeConfig.ALL_THEMES:
        print(f"测试主题: {theme['name']}")
        
        # 设置主题
        theme_manager.set_theme(theme['name'])
        time.sleep(0.1)
        app.processEvents()
        
        # 检查Config.COLORS是否正确更新
        assert Config.COLORS['background'] == theme['background']
        assert Config.COLORS['primary'] == theme['primary']
        assert Config.COLORS['text_primary'] == theme['text_primary']
        
        print(f"  ✓ {theme['name']} 配色更新正确")
    
    print("✓ 所有主题配色完整性测试通过")
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("🔧 T-1节拍器修复验证测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 6
    
    try:
        # 测试窗口大小修复
        if test_window_size_fix():
            success_count += 1
        
        # 测试增强的烟花效果
        if test_enhanced_fireworks():
            success_count += 1
        
        # 测试QPainter修复
        if test_painter_fix():
            success_count += 1
        
        # 测试主题过渡稳定性
        if test_theme_transition_stability():
            success_count += 1
        
        # 测试烟花性能
        if test_fireworks_performance():
            success_count += 1
        
        # 测试颜色主题完整性
        if test_color_theme_completeness():
            success_count += 1
        
        print("\n" + "=" * 50)
        print(f"测试结果: {success_count}/{total_tests} 通过")
        
        if success_count == total_tests:
            print("✅ 所有修复验证测试通过！")
            print("\n🎉 修复总结:")
            print("• 修复了主题切换时窗口大小变化问题")
            print("• 增强了烟花效果，更加绚丽明显")
            print("• 修复了QPainter错误，绘制更稳定")
            print("• 优化了主题过渡的稳定性")
            print("• 提升了烟花动画的性能")
            print("• 确保了所有主题的完整性")
            print("\n🎆 烟花增强特性:")
            print("• 增加粒子数量 (25-40个/爆炸)")
            print("• 添加拖尾效果和发光效果")
            print("• 更频繁的烟花发射 (0.5秒间隔)")
            print("• 大型烟花特效 (50-80个粒子)")
            print("• 多重烟花定时器 (1.5秒间隔)")
            print("• 更强的闪烁和颜色效果")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
