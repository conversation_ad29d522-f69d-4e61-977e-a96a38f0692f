#!/usr/bin/env python3
"""
T-1节拍器烟花效果测试
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_fireworks_celebration():
    """测试烟花庆祝功能"""
    print("🎆 测试烟花庆祝功能")
    print("-" * 30)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建庆祝组件
    celebration = CelebrationWidget()
    celebration.resize(360, 360)
    
    print("✓ 庆祝组件创建成功")
    
    # 测试烟花庆祝方法
    assert hasattr(celebration, 'celebrate_success')
    assert hasattr(celebration, 'stop_fireworks_celebration')
    assert hasattr(celebration, '_start_fireworks_celebration')
    assert hasattr(celebration, '_launch_firework')
    print("✓ 烟花庆祝方法存在")
    
    # 测试初始状态
    assert not celebration.is_celebrating
    assert not celebration.continuous_fireworks
    print("✓ 初始状态正确")
    
    # 测试启动烟花庆祝
    celebration.celebrate_success()
    assert celebration.is_celebrating
    assert celebration.continuous_fireworks
    assert celebration.celebration_type == "success"
    print("✓ 烟花庆祝启动成功")
    
    # 测试粒子系统
    app.processEvents()  # 处理事件
    time.sleep(0.1)
    app.processEvents()
    
    # 应该有粒子生成
    assert len(celebration.particles) > 0
    print(f"✓ 粒子系统工作正常，当前粒子数: {len(celebration.particles)}")
    
    # 测试停止烟花庆祝
    celebration.stop_fireworks_celebration()
    assert not celebration.continuous_fireworks
    print("✓ 烟花庆祝停止成功")
    
    return True

def test_firework_particles():
    """测试烟花粒子效果"""
    print("\n✨ 测试烟花粒子效果")
    print("-" * 30)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    
    if not QApplication.instance():
        app = QApplication([])
    
    celebration = CelebrationWidget()
    celebration.resize(360, 360)
    
    # 手动发射烟花
    celebration.continuous_fireworks = True
    celebration._launch_firework()
    
    print(f"发射烟花后粒子数: {len(celebration.particles)}")
    assert len(celebration.particles) > 0
    
    # 检查粒子属性
    particle = celebration.particles[0]
    required_attrs = ['x', 'y', 'vx', 'vy', 'size', 'life', 'color', 'gravity', 'fade_rate', 'sparkle']
    
    for attr in required_attrs:
        assert attr in particle, f"粒子缺少属性: {attr}"
    
    print("✓ 粒子属性完整")
    
    # 测试粒子颜色
    colors_found = set()
    for _ in range(10):
        celebration._launch_firework()
        for particle in celebration.particles[-5:]:  # 检查最新的5个粒子
            color = particle['color']
            colors_found.add(color.name())
    
    print(f"✓ 发现 {len(colors_found)} 种不同颜色的烟花")
    assert len(colors_found) > 1, "烟花颜色应该有多种"
    
    # 测试闪烁效果
    sparkle_count = sum(1 for p in celebration.particles if p.get('sparkle', False))
    print(f"✓ 闪烁粒子数: {sparkle_count}")
    
    return True

def test_fireworks_performance():
    """测试烟花性能"""
    print("\n⚡ 测试烟花性能")
    print("-" * 30)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    
    if not QApplication.instance():
        app = QApplication([])
    
    celebration = CelebrationWidget()
    celebration.resize(360, 360)
    celebration.continuous_fireworks = True
    
    # 发射大量烟花测试性能
    start_time = time.time()
    for _ in range(20):
        celebration._launch_firework()
    
    launch_time = time.time() - start_time
    print(f"发射20个烟花耗时: {launch_time:.3f}秒")
    
    initial_count = len(celebration.particles)
    print(f"初始粒子数: {initial_count}")
    
    # 模拟动画更新
    start_time = time.time()
    for _ in range(100):
        celebration._update_animation()
    
    update_time = time.time() - start_time
    print(f"100次动画更新耗时: {update_time:.3f}秒")
    
    final_count = len(celebration.particles)
    print(f"最终粒子数: {final_count}")
    
    # 检查粒子数量限制
    assert final_count <= 200, "粒子数量应该被限制在200以内"
    print("✓ 粒子数量限制正常")
    
    return True

def test_integration_with_main_window():
    """测试与主窗口的集成"""
    print("\n🏠 测试与主窗口的集成")
    print("-" * 30)
    
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    if not QApplication.instance():
        app = QApplication([])
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 检查庆祝组件是否存在
    assert hasattr(main_window.comparison_widget, 'celebration_widget')
    celebration = main_window.comparison_widget.celebration_widget
    
    print("✓ 主窗口包含庆祝组件")
    
    # 测试目标达成回调
    assert hasattr(main_window, 'on_cycle_completed')
    print("✓ 周期完成回调存在")
    
    # 测试开始新周期时停止烟花
    assert hasattr(main_window, 'on_start_stop_clicked')
    print("✓ 开始/停止回调存在")
    
    # 模拟目标达成
    celebration.celebrate_success()
    assert celebration.continuous_fireworks
    print("✓ 目标达成触发烟花")
    
    # 模拟开始新周期
    celebration.stop_fireworks_celebration()
    assert not celebration.continuous_fireworks
    print("✓ 新周期开始停止烟花")
    
    return True

def demo_fireworks_colors():
    """演示烟花颜色"""
    print("\n🌈 烟花颜色演示")
    print("-" * 30)
    
    from PyQt6.QtWidgets import QApplication
    from ui.celebration import CelebrationWidget
    from PyQt6.QtGui import QColor
    
    if not QApplication.instance():
        app = QApplication([])
    
    celebration = CelebrationWidget()
    
    # 获取烟花颜色列表
    firework_colors = [
        ("#FFD700", "金色"),
        ("#FF6B6B", "红色"),
        ("#4CCD99", "绿色"),
        ("#2196F3", "蓝色"),
        ("#9C27B0", "紫色"),
        ("#FF9800", "橙色"),
        ("#00BCD4", "青色"),
        ("#FFEB3B", "黄色"),
    ]
    
    print("烟花颜色配置:")
    for color_code, color_name in firework_colors:
        color = QColor(color_code)
        print(f"  {color_name}: {color_code} (RGB: {color.red()}, {color.green()}, {color.blue()})")
    
    print(f"\n总共 {len(firework_colors)} 种颜色")
    print("每次烟花发射时随机选择颜色")

def main():
    """主测试函数"""
    print("=" * 50)
    print("🎆 T-1节拍器烟花效果测试")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    try:
        # 测试烟花庆祝功能
        if test_fireworks_celebration():
            success_count += 1
        
        # 测试烟花粒子效果
        if test_firework_particles():
            success_count += 1
        
        # 测试烟花性能
        if test_fireworks_performance():
            success_count += 1
        
        # 测试与主窗口的集成
        if test_integration_with_main_window():
            success_count += 1
        
        # 演示烟花颜色
        demo_fireworks_colors()
        
        print("\n" + "=" * 50)
        print(f"测试结果: {success_count}/{total_tests} 通过")
        
        if success_count == total_tests:
            print("✅ 所有烟花效果测试通过！")
            print("\n🎆 烟花特效功能:")
            print("• 目标达成时自动触发烟花绽放")
            print("• 8种随机颜色的绚丽烟花")
            print("• 闪烁粒子效果增强视觉冲击")
            print("• 持续到下一个10分钟节拍开始")
            print("• 性能优化，粒子数量自动控制")
            print("• 重力效果模拟真实烟花轨迹")
            print("\n🚀 使用方法:")
            print("1. 运行程序: python main.py")
            print("2. 设置目标并开始周期")
            print("3. 达成目标时自动触发烟花")
            print("4. 开始新周期时烟花自动停止")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    return 0 if success_count == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
