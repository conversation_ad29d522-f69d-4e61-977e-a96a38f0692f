#!/usr/bin/env python3
"""
T-1节拍器 - 制造业生产线产量对比仪表盘
主程序入口

功能特点:
- 实时产量对比显示
- 动态环形进度条
- 智能颜色反馈系统
- 庆祝动画效果
- 生产数据模拟
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QFontDatabase

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow
from core.config import Config

def setup_application():
    """设置应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("T-1节拍器")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("制造业解决方案")
    app.setOrganizationDomain("manufacturing.com")
    
    # 设置应用程序样式
    app.setStyle('Fusion')  # 使用Fusion样式获得更好的外观
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 10)  # 使用微软雅黑字体
    app.setFont(font)
    
    # 启用高DPI支持 (PyQt6中已默认启用)
    # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    return app

def check_dependencies():
    """检查依赖项"""
    try:
        import PyQt6
        import numpy
        print("✓ 所有依赖项检查通过")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖项: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def create_assets_directory():
    """创建资源目录"""
    assets_dir = os.path.join(os.path.dirname(__file__), 'assets')
    sounds_dir = os.path.join(assets_dir, 'sounds')
    fonts_dir = os.path.join(assets_dir, 'fonts')
    
    for directory in [assets_dir, sounds_dir, fonts_dir]:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

def main():
    """主函数"""
    print("=" * 50)
    print("T-1节拍器 - 制造业生产线产量对比仪表盘")
    print("版本: 1.0")
    print("=" * 50)
    
    # 检查依赖项
    if not check_dependencies():
        input("按回车键退出...")
        return 1
    
    # 创建资源目录
    create_assets_directory()
    
    # 创建应用程序
    app = setup_application()
    
    try:
        # 创建主窗口
        print("正在启动主窗口...")
        main_window = MainWindow()
        
        # 显示窗口
        main_window.show()
        
        # 显示启动信息
        print("✓ T-1节拍器启动成功!")
        print(f"窗口大小: {Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
        print("使用说明:")
        print("1. 设置目标产量")
        print("2. 点击'开始'按钮启动生产周期")
        print("3. 观察实时产量对比和进度显示")
        print("4. 可使用'手动+1'按钮模拟手动计数")
        print("5. 按F11切换全屏模式")
        print("-" * 30)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        
        # 显示错误对话框
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Icon.Critical)
        error_msg.setWindowTitle("启动错误")
        error_msg.setText("T-1节拍器启动失败")
        error_msg.setDetailedText(str(e))
        error_msg.exec()
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
