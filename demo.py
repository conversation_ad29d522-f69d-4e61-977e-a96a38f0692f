#!/usr/bin/env python3
"""
T-1节拍器演示脚本
展示主要功能和特性
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_color_theme():
    """演示颜色主题系统"""
    print("🎨 颜色主题系统演示")
    print("-" * 30)
    
    from core.color_theme import ColorThemeManager
    
    color_manager = ColorThemeManager()
    
    # 测试不同进度的颜色主题
    test_cases = [
        (30, 100, "低进度"),
        (60, 100, "中等进度"),
        (85, 100, "接近目标"),
        (100, 100, "刚好达标"),
        (120, 100, "超额完成")
    ]
    
    for current, target, description in test_cases:
        text_color, bg_color, hint = color_manager.get_color_theme(current, target)
        progress_color = color_manager.get_progress_color(current, target)
        symbol_color = color_manager.get_symbol_color(current, target)
        
        print(f"{description} ({current}/{target}):")
        print(f"  提示: {hint}")
        print(f"  背景色: {bg_color}")
        print(f"  进度色: {progress_color}")
        print(f"  符号色: {symbol_color}")
        print()

def demo_data_simulation():
    """演示数据模拟功能"""
    print("📊 数据模拟系统演示")
    print("-" * 30)
    
    from core.data_manager import ProductionDataManager
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import QTimer
    
    # 创建应用程序实例
    if not QApplication.instance():
        app = QApplication([])
    
    data_manager = ProductionDataManager()
    data_manager.set_target(50)  # 设置较小目标便于演示
    
    print("模拟5秒钟的生产数据...")
    print("目标产量: 50")
    print()
    
    # 连接信号
    def on_data_updated(current, target, time_remaining):
        progress = (current / target) * 100 if target > 0 else 0
        rate = data_manager.get_production_rate()
        print(f"时间: {data_manager.get_time_formatted()} | "
              f"产量: {current}/{target} | "
              f"进度: {progress:.1f}% | "
              f"速率: {rate:.1f}件/分钟")
    
    def on_milestone(percentage):
        print(f"🎉 里程碑达成: {percentage:.0f}%")
    
    data_manager.data_updated.connect(on_data_updated)
    data_manager.milestone_reached.connect(on_milestone)
    
    # 启动模拟
    data_manager.start_cycle()
    
    # 运行5秒
    start_time = time.time()
    while time.time() - start_time < 5:
        app.processEvents()
        time.sleep(0.1)
    
    data_manager.stop_cycle()
    print("\n模拟结束")

def demo_config_system():
    """演示配置系统"""
    print("⚙️ 配置系统演示")
    print("-" * 30)
    
    from core.config import Config, ThemeConfig
    
    print("基本配置:")
    print(f"  窗口大小: {Config.WINDOW_WIDTH}x{Config.WINDOW_HEIGHT}")
    print(f"  默认目标: {Config.DEFAULT_TARGET}")
    print(f"  更新间隔: {Config.UPDATE_INTERVAL}ms")
    print(f"  周期时长: {Config.COUNTDOWN_DURATION}秒")
    print()
    
    print("颜色配置:")
    for name, color in Config.COLORS.items():
        print(f"  {name}: {color}")
    print()
    
    print("字体配置:")
    for name, font_config in Config.FONTS.items():
        print(f"  {name}: {font_config}")
    print()
    
    print("阈值配置:")
    for name, threshold in Config.THRESHOLDS.items():
        print(f"  {name}: {threshold}")
    print()
    
    print("主题配置:")
    print("  深色主题:", ThemeConfig.DARK_THEME)
    print("  浅色主题:", ThemeConfig.LIGHT_THEME)

def demo_progress_calculation():
    """演示进度计算"""
    print("📈 进度计算演示")
    print("-" * 30)
    
    from core.color_theme import ColorThemeManager
    
    color_manager = ColorThemeManager()
    
    print("渐变色计算示例:")
    for i in range(0, 11):
        ratio = i / 10.0
        color = color_manager.gradient_red_to_yellow(ratio)
        print(f"  进度 {ratio*100:3.0f}%: {color}")
    print()
    
    print("冲刺模式判断:")
    test_scenarios = [
        (40, 100, 300, "正常时间，低进度"),
        (40, 100, 100, "剩余时间少，低进度 -> 冲刺模式"),
        (85, 100, 100, "剩余时间少，高进度"),
        (95, 100, 60, "最后1分钟，接近完成")
    ]
    
    for current, target, time_remaining, description in test_scenarios:
        is_sprint = color_manager.is_sprint_mode(current, target, time_remaining)
        print(f"  {description}: {'是' if is_sprint else '否'}")

def demo_ui_components():
    """演示UI组件"""
    print("🖼️ UI组件演示")
    print("-" * 30)
    
    try:
        from PyQt6.QtWidgets import QApplication
        
        if not QApplication.instance():
            app = QApplication([])
        
        print("创建UI组件...")
        
        # 测试进度环组件
        from ui.progress_ring import ProgressRing
        progress_ring = ProgressRing()
        progress_ring.set_colors(['#FF6B6B', '#FFD700'])
        progress_ring.animate_to(75.0)
        print("✓ 环形进度条组件创建成功")
        
        # 测试庆祝组件
        from ui.celebration import CelebrationWidget
        celebration = CelebrationWidget()
        print("✓ 庆祝动画组件创建成功")
        
        # 测试对比组件
        from ui.comparison_widget import ComparisonWidget
        comparison = ComparisonWidget()
        comparison.update_data(75, 100, 300)
        print("✓ 对比显示组件创建成功")
        
        print("所有UI组件创建成功！")
        
    except Exception as e:
        print(f"UI组件测试失败: {e}")

def main():
    """主演示函数"""
    print("=" * 50)
    print("🎯 T-1节拍器功能演示")
    print("=" * 50)
    print()
    
    try:
        # 演示配置系统
        demo_config_system()
        print()
        
        # 演示颜色主题
        demo_color_theme()
        print()
        
        # 演示进度计算
        demo_progress_calculation()
        print()
        
        # 演示UI组件
        demo_ui_components()
        print()
        
        # 演示数据模拟（需要用户确认）
        response = input("是否运行数据模拟演示？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print()
            demo_data_simulation()
        
        print()
        print("=" * 50)
        print("✅ 演示完成！")
        print()
        print("主要功能特点:")
        print("• 智能颜色反馈系统")
        print("• 实时数据模拟")
        print("• 动态进度显示")
        print("• 庆祝动画效果")
        print("• 模块化配置系统")
        print()
        print("运行主程序: python main.py")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
