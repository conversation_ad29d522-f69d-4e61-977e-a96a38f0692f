"""
T-1节拍器颜色主题系统
智能颜色反馈和渐变计算
"""

import math
from typing import <PERSON>ple, Dict
from .config import Config

class ColorThemeManager:
    """颜色主题管理器"""
    
    def __init__(self):
        self.colors = Config.COLORS
        self.thresholds = Config.THRESHOLDS
    
    def get_color_theme(self, current: int, target: int) -> Tuple[str, str, str]:
        """
        根据当前产量和目标值获取颜色主题
        
        Args:
            current: 当前产量
            target: 目标产量
            
        Returns:
            Tuple[文字颜色, 背景颜色, 提示文字]
        """
        if target == 0:
            ratio = 0
        else:
            ratio = current / target
        
        if ratio >= self.thresholds['success']:
            return (
                self.colors['text_light'], 
                self.colors['success'], 
                "✔ 超额完成!"
            )
        elif ratio >= self.thresholds['warning']:
            return (
                self.colors['text_dark'], 
                self.colors['warning'], 
                "➤ 接近目标!"
            )
        else:
            return (
                self.colors['text_light'], 
                self.colors['danger'], 
                "✘ 加速中..."
            )
    
    def get_progress_color(self, current: int, target: int) -> str:
        """
        获取进度条颜色
        
        Args:
            current: 当前产量
            target: 目标产量
            
        Returns:
            进度条颜色
        """
        if target == 0:
            return self.colors['danger']
        
        ratio = current / target
        
        if ratio >= self.thresholds['success']:
            return self.colors['success']
        elif ratio >= self.thresholds['warning']:
            return self.colors['warning']
        else:
            # 红黄渐变
            return self.gradient_red_to_yellow(ratio)
    
    def gradient_red_to_yellow(self, ratio: float) -> str:
        """
        计算红色到黄色的渐变色
        
        Args:
            ratio: 进度比例 (0-1)
            
        Returns:
            十六进制颜色值
        """
        # 确保ratio在0-1范围内
        ratio = max(0, min(1, ratio))
        
        # 红色 (255, 107, 107) 到 黄色 (255, 215, 0)
        red = 255
        green = int(107 + (215 - 107) * ratio)
        blue = int(107 * (1 - ratio))
        
        return f"#{red:02x}{green:02x}{blue:02x}"
    
    def get_symbol_color(self, current: int, target: int) -> str:
        """
        获取VS符号颜色
        
        Args:
            current: 当前产量
            target: 目标产量
            
        Returns:
            符号颜色
        """
        if current >= target:
            return self.colors['success']
        else:
            ratio = current / target if target > 0 else 0
            return self.gradient_red_to_yellow(ratio)
    
    def get_ring_gradient(self, progress: float) -> list:
        """
        获取环形进度条渐变色列表
        
        Args:
            progress: 进度值 (0-100)
            
        Returns:
            渐变色列表
        """
        progress_ratio = progress / 100.0
        
        if progress_ratio >= self.thresholds['success']:
            # 绿色渐变
            return [self.colors['success'], '#66D9A6']
        elif progress_ratio >= self.thresholds['warning']:
            # 金色渐变
            return [self.colors['warning'], '#FFE55C']
        else:
            # 红黄渐变
            start_color = self.colors['danger']
            end_color = self.gradient_red_to_yellow(progress_ratio)
            return [start_color, end_color]
    
    def get_celebration_colors(self) -> list:
        """
        获取庆祝动画颜色序列
        
        Returns:
            庆祝颜色列表
        """
        return [
            self.colors['success'],
            self.colors['warning'],
            self.colors['primary'],
            '#FF69B4',  # 粉色
            '#9C27B0',  # 紫色
            '#00BCD4'   # 青色
        ]
    
    def is_sprint_mode(self, current: int, target: int, time_remaining: int) -> bool:
        """
        判断是否应该进入冲刺模式
        
        Args:
            current: 当前产量
            target: 目标产量
            time_remaining: 剩余时间(秒)
            
        Returns:
            是否进入冲刺模式
        """
        if target == 0:
            return False
        
        ratio = current / target
        # 最后2分钟且进度低于80%
        return time_remaining <= 120 and ratio < self.thresholds['sprint_mode']
    
    def get_pulse_color(self, base_color: str, intensity: float) -> str:
        """
        获取脉冲效果颜色
        
        Args:
            base_color: 基础颜色
            intensity: 强度 (0-1)
            
        Returns:
            脉冲颜色
        """
        # 简单的亮度调整
        if base_color.startswith('#'):
            hex_color = base_color[1:]
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            
            # 增加亮度
            factor = 1 + intensity * 0.3
            r = min(255, int(r * factor))
            g = min(255, int(g * factor))
            b = min(255, int(b * factor))
            
            return f"#{r:02x}{g:02x}{b:02x}"
        
        return base_color
