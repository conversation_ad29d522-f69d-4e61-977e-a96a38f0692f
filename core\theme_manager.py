"""
T-1节拍器主题管理模块
"""

from PyQt6.QtCore import QObject, pyqtSignal
from .config import Config, ThemeConfig

class ThemeManager(QObject):
    """主题管理器"""
    
    # 主题变更信号
    theme_changed = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.current_theme = ThemeConfig.DARK_THEME
        self.available_themes = ThemeConfig.ALL_THEMES
    
    def get_current_theme(self) -> dict:
        """获取当前主题"""
        return self.current_theme
    
    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        return self.available_themes
    
    def set_theme(self, theme_name: str):
        """设置主题"""
        theme = ThemeConfig.get_theme_by_name(theme_name)
        if theme != self.current_theme:
            self.current_theme = theme
            # 更新Config中的颜色
            self._update_config_colors()
            # 发送主题变更信号
            self.theme_changed.emit(self.current_theme)
            print(f"主题已切换到: {theme['name']}")
    
    def _update_config_colors(self):
        """更新配置中的颜色"""
        Config.COLORS.update({
            'background': self.current_theme['background'],
            'surface': self.current_theme['surface'],
            'primary': self.current_theme['primary'],
            'success': self.current_theme['success'],
            'warning': self.current_theme['warning'],
            'danger': self.current_theme['danger'],
            'text_light': self.current_theme['text_light'],
            'text_dark': self.current_theme['text_dark'],
            'border': self.current_theme['border']
        })
    
    def get_next_theme(self) -> dict:
        """获取下一个主题（用于循环切换）"""
        current_index = 0
        for i, theme in enumerate(self.available_themes):
            if theme['name'] == self.current_theme['name']:
                current_index = i
                break
        
        next_index = (current_index + 1) % len(self.available_themes)
        return self.available_themes[next_index]
    
    def switch_to_next_theme(self):
        """切换到下一个主题"""
        next_theme = self.get_next_theme()
        self.set_theme(next_theme['name'])
    
    def get_theme_names(self) -> list:
        """获取所有主题名称"""
        return [theme['name'] for theme in self.available_themes]
