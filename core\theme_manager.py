"""
T-1节拍器主题管理模块
"""

from PyQt6.QtCore import QObject, pyqtSignal, QPropertyAnimation, QEasingCurve, QTimer
from PyQt6.QtWidgets import QGraphicsOpacityEffect
from .config import Config, ThemeConfig

class ThemeManager(QObject):
    """主题管理器"""

    # 主题变更信号
    theme_changed = pyqtSignal(dict)
    theme_transition_start = pyqtSignal()
    theme_transition_end = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.current_theme = ThemeConfig.DARK_THEME
        self.available_themes = ThemeConfig.ALL_THEMES
        self.is_transitioning = False
    
    def get_current_theme(self) -> dict:
        """获取当前主题"""
        return self.current_theme
    
    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        return self.available_themes
    
    def set_theme(self, theme_name: str):
        """设置主题（带过渡效果）"""
        if self.is_transitioning:
            return  # 如果正在过渡中，忽略新的切换请求

        theme = ThemeConfig.get_theme_by_name(theme_name)
        if theme != self.current_theme:
            self.is_transitioning = True
            self.theme_transition_start.emit()

            # 延迟应用新主题，创建过渡效果
            QTimer.singleShot(150, lambda: self._apply_new_theme(theme))

    def _apply_new_theme(self, theme):
        """应用新主题"""
        self.current_theme = theme
        # 更新Config中的颜色
        self._update_config_colors()
        # 发送主题变更信号
        self.theme_changed.emit(self.current_theme)

        # 延迟结束过渡状态
        QTimer.singleShot(150, self._end_transition)
        print(f"主题已切换到: {theme['name']}")

    def _end_transition(self):
        """结束过渡状态"""
        self.is_transitioning = False
        self.theme_transition_end.emit()
    
    def _update_config_colors(self):
        """更新配置中的颜色"""
        Config.COLORS.update({
            'background': self.current_theme['background'],
            'surface': self.current_theme['surface'],
            'card_bg': self.current_theme['card_bg'],
            'primary': self.current_theme['primary'],
            'primary_light': self.current_theme['primary_light'],
            'success': self.current_theme['success'],
            'warning': self.current_theme['warning'],
            'danger': self.current_theme['danger'],
            'text_primary': self.current_theme['text_primary'],
            'text_secondary': self.current_theme['text_secondary'],
            'text_on_primary': self.current_theme['text_on_primary'],
            'text_on_surface': self.current_theme['text_on_surface'],
            'border': self.current_theme['border'],
            'border_light': self.current_theme['border_light'],
            # 保持向后兼容
            'text_light': self.current_theme['text_primary'],
            'text_dark': self.current_theme['text_secondary']
        })
    
    def get_next_theme(self) -> dict:
        """获取下一个主题（用于循环切换）"""
        current_index = 0
        for i, theme in enumerate(self.available_themes):
            if theme['name'] == self.current_theme['name']:
                current_index = i
                break
        
        next_index = (current_index + 1) % len(self.available_themes)
        return self.available_themes[next_index]
    
    def switch_to_next_theme(self):
        """切换到下一个主题"""
        next_theme = self.get_next_theme()
        self.set_theme(next_theme['name'])
    
    def get_theme_names(self) -> list:
        """获取所有主题名称"""
        return [theme['name'] for theme in self.available_themes]
