"""
淡入淡出过渡效果组件
"""

from PyQt6.QtCore import QObject, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtWidgets import QGraphicsOpacityEffect

class FadeTransition(QObject):
    """淡入淡出过渡效果管理器"""
    
    def __init__(self, widget, parent=None):
        super().__init__(parent)
        self.widget = widget
        self.opacity_effect = QGraphicsOpacityEffect()
        self.widget.setGraphicsEffect(self.opacity_effect)
        
        # 透明度动画
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(300)  # 300ms过渡时间
        self.fade_animation.setEasingCurve(QEasingCurve.Type.InOutQuad)
        
        # 初始透明度
        self.opacity_effect.setOpacity(1.0)
    
    def fade_out(self, callback=None):
        """淡出效果"""
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        
        if callback:
            self.fade_animation.finished.connect(callback)
        
        self.fade_animation.start()
    
    def fade_in(self, callback=None):
        """淡入效果"""
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        
        if callback:
            self.fade_animation.finished.connect(callback)
        
        self.fade_animation.start()
    
    def fade_out_in(self, middle_callback=None, end_callback=None):
        """淡出后淡入的完整过渡"""
        def on_fade_out_finished():
            # 淡出完成后执行中间回调
            if middle_callback:
                middle_callback()
            
            # 然后淡入
            self.fade_animation.finished.disconnect()
            if end_callback:
                self.fade_animation.finished.connect(end_callback)
            self.fade_in()
        
        self.fade_out(on_fade_out_finished)
    
    def set_opacity(self, opacity):
        """直接设置透明度"""
        self.opacity_effect.setOpacity(opacity)
    
    def get_opacity(self):
        """获取当前透明度"""
        return self.opacity_effect.opacity()
