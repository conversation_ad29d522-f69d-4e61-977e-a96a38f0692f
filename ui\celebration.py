"""
T-1节拍器庆祝动画效果组件
"""

import math
import random
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtGui import QPainter, QColor, QFont, QPen, QBrush
from PyQt6.QtCore import Qt
from core.config import Config
from core.color_theme import ColorThemeManager

class CelebrationWidget(QWidget):
    """庆祝动画组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        self.color_manager = ColorThemeManager()
        self.celebration_colors = self.color_manager.get_celebration_colors()

        # 动画状态
        self.is_celebrating = False
        self.celebration_type = "success"  # success, milestone
        self.continuous_fireworks = False  # 持续烟花标志

        # 粒子系统
        self.particles = []
        self.fireworks = []
        
        # 文字动画
        self.text_scale = 0.0
        self.text_rotation = 0.0
        self.text_opacity = 0.0
        self.celebration_text = ""
        
        # 定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._update_animation)
        
        # 属性动画
        self.text_animation = QPropertyAnimation(self, b"textScale")
        self.text_animation.setDuration(Config.ANIMATION['celebration_duration'])
        self.text_animation.setEasingCurve(QEasingCurve.Type.OutElastic)
        
        self.opacity_animation = QPropertyAnimation(self, b"textOpacity")
        self.opacity_animation.setDuration(Config.ANIMATION['celebration_duration'])
        
        # 背景脉冲
        self.pulse_intensity = 0.0
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self._update_pulse)
        
        self.setStyleSheet("background: transparent;")
        self.hide()
    
    @pyqtProperty(float)
    def textScale(self):
        return self.text_scale
    
    @textScale.setter
    def textScale(self, value):
        self.text_scale = value
        self.update()
    
    @pyqtProperty(float)
    def textOpacity(self):
        return self.text_opacity
    
    @textOpacity.setter
    def textOpacity(self, value):
        self.text_opacity = value
        self.update()
    
    def celebrate_success(self):
        """触发成功庆祝动画 - 烟花绽放"""
        self.celebration_type = "success"
        self.celebration_text = "目标达成!"
        self._start_fireworks_celebration()
    
    def celebrate_milestone(self, percentage: float):
        """触发里程碑庆祝动画"""
        self.celebration_type = "milestone"
        self.celebration_text = f"{int(percentage)}% 完成!"
        self._start_mini_celebration()
    
    def celebrate_sprint_mode(self):
        """触发冲刺模式动画"""
        self.celebration_type = "sprint"
        self.celebration_text = "冲刺模式!"
        self._start_warning_animation()
    
    def _start_celebration(self):
        """开始完整庆祝动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建烟花粒子
        self._create_fireworks()
        
        # 启动动画定时器
        self.animation_timer.start(50)  # 20 FPS
        
        # 文字动画
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.5)
        self.text_animation.finished.connect(self._on_celebration_finished)
        self.text_animation.start()
        
        # 透明度动画
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        # 背景脉冲
        self.pulse_timer.start(100)
        
        # 3秒后自动结束
        QTimer.singleShot(Config.ANIMATION['celebration_duration'], self._stop_celebration)
    
    def _start_mini_celebration(self):
        """开始小型庆祝动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建少量粒子
        self._create_mini_particles()
        
        self.animation_timer.start(50)
        
        # 简短的文字动画
        self.text_animation.setDuration(1000)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.0)
        self.text_animation.start()
        
        self.opacity_animation.setDuration(1000)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(0.8)
        self.opacity_animation.start()
        
        # 1秒后结束
        QTimer.singleShot(1000, self._stop_celebration)
    
    def _start_warning_animation(self):
        """开始警告动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建警告粒子
        self._create_warning_particles()
        
        self.animation_timer.start(50)
        
        # 快速闪烁动画
        self.text_animation.setDuration(500)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.2)
        self.text_animation.start()
        
        self.opacity_animation.setDuration(500)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        # 快速脉冲
        self.pulse_timer.start(50)
        
        QTimer.singleShot(1500, self._stop_celebration)

    def _start_fireworks_celebration(self):
        """开始烟花庆祝动画 - 持续到下一个周期"""
        self.is_celebrating = True
        self.show()

        # 设置为持续模式
        self.continuous_fireworks = True

        # 启动动画定时器
        self.animation_timer.start(50)  # 20 FPS

        # 文字动画
        self.text_animation.setDuration(2000)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.5)
        self.text_animation.start()

        # 透明度动画
        self.opacity_animation.setDuration(2000)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()

        # 背景脉冲
        self.pulse_timer.start(100)

        # 启动烟花发射定时器
        self.fireworks_timer = QTimer()
        self.fireworks_timer.timeout.connect(self._launch_firework)
        self.fireworks_timer.start(800)  # 每0.8秒发射一次烟花

        # 初始烟花
        self._launch_multiple_fireworks()

        print("🎆 烟花庆祝开始！将持续到下一个周期开始...")

    def stop_fireworks_celebration(self):
        """停止烟花庆祝动画"""
        if hasattr(self, 'continuous_fireworks') and self.continuous_fireworks:
            self.continuous_fireworks = False
            if hasattr(self, 'fireworks_timer'):
                self.fireworks_timer.stop()
            self._stop_celebration()
            print("🎆 烟花庆祝结束")

    def _launch_firework(self):
        """发射单个烟花"""
        if not self.continuous_fireworks:
            return

        center_x = self.width() // 2
        center_y = self.height() // 2

        # 随机选择爆炸点
        explosion_x = center_x + random.randint(-100, 100)
        explosion_y = center_y + random.randint(-80, 80)

        # 随机选择烟花颜色
        firework_colors = [
            QColor("#FFD700"),  # 金色
            QColor("#FF6B6B"),  # 红色
            QColor("#4CCD99"),  # 绿色
            QColor("#2196F3"),  # 蓝色
            QColor("#9C27B0"),  # 紫色
            QColor("#FF9800"),  # 橙色
            QColor("#00BCD4"),  # 青色
            QColor("#FFEB3B"),  # 黄色
        ]

        firework_color = random.choice(firework_colors)

        # 创建烟花粒子
        particle_count = random.randint(15, 25)
        for i in range(particle_count):
            angle = (i / particle_count) * 2 * math.pi + random.uniform(-0.2, 0.2)
            speed = random.uniform(2, 6)

            particle = {
                'x': explosion_x,
                'y': explosion_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(3, 8),
                'life': 1.0,
                'color': QColor(firework_color),
                'gravity': random.uniform(0.05, 0.15),
                'fade_rate': random.uniform(0.015, 0.025),
                'sparkle': random.random() < 0.3  # 30%概率闪烁
            }
            self.particles.append(particle)

    def _launch_multiple_fireworks(self):
        """发射多个烟花"""
        for _ in range(3):
            self._launch_firework()

    def _create_fireworks(self):
        """创建烟花效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # 创建多个烟花爆炸点
        for _ in range(3):
            explosion_x = center_x + random.randint(-200, 200)
            explosion_y = center_y + random.randint(-150, 150)
            
            # 每个爆炸点创建粒子
            for i in range(30):
                angle = (i / 30) * 2 * math.pi
                speed = random.uniform(3, 8)
                
                particle = {
                    'x': explosion_x,
                    'y': explosion_y,
                    'vx': speed * math.cos(angle),
                    'vy': speed * math.sin(angle),
                    'size': random.uniform(4, 12),
                    'life': 1.0,
                    'color': QColor(random.choice(self.celebration_colors)),
                    'gravity': 0.1
                }
                self.particles.append(particle)
    
    def _create_mini_particles(self):
        """创建小型粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i in range(10):
            angle = (i / 10) * 2 * math.pi
            speed = random.uniform(2, 4)
            
            particle = {
                'x': center_x,
                'y': center_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(3, 6),
                'life': 1.0,
                'color': QColor(Config.COLORS['warning']),
                'gravity': 0.05
            }
            self.particles.append(particle)
    
    def _create_warning_particles(self):
        """创建警告粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i in range(15):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(3, 6)
            
            particle = {
                'x': center_x,
                'y': center_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(4, 8),
                'life': 1.0,
                'color': QColor(Config.COLORS['danger']),
                'gravity': 0.08
            }
            self.particles.append(particle)
    
    def _update_animation(self):
        """更新动画状态"""
        # 更新粒子
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']

            # 重力效果
            if 'gravity' in particle:
                particle['vy'] += particle['gravity']
            else:
                particle['vy'] += 0.1  # 默认重力

            # 生命值衰减
            if 'fade_rate' in particle:
                particle['life'] -= particle['fade_rate']
            else:
                particle['life'] -= 0.02

            # 大小变化
            particle['size'] *= 0.99

            # 闪烁效果
            if particle.get('sparkle', False):
                # 闪烁粒子的透明度变化
                sparkle_alpha = (math.sin(particle['life'] * 20) + 1) / 2
                alpha_value = max(0.0, min(1.0, particle['life'] * sparkle_alpha))
                particle['color'].setAlphaF(alpha_value)
            else:
                # 普通粒子的透明度
                alpha_value = max(0.0, min(1.0, particle['life']))
                particle['color'].setAlphaF(alpha_value)

            # 移除死亡粒子
            if particle['life'] <= 0 or particle['size'] < 1:
                self.particles.remove(particle)

        # 更新文字旋转
        if self.celebration_type == "success":
            self.text_rotation += 1  # 减慢旋转速度

        # 限制粒子数量以保持性能
        if len(self.particles) > 200:
            # 移除最老的粒子
            self.particles = self.particles[-150:]

        self.update()
    
    def _update_pulse(self):
        """更新脉冲效果"""
        import time
        if self.celebration_type == "sprint":
            # 快速脉冲
            self.pulse_intensity = (math.sin(time.time() * 10) + 1) / 2
        else:
            # 慢速脉冲
            self.pulse_intensity = (math.sin(time.time() * 3) + 1) / 2
        
        self.update()
    
    def _stop_celebration(self):
        """停止庆祝动画"""
        self.is_celebrating = False
        self.animation_timer.stop()
        self.pulse_timer.stop()
        self.particles.clear()
        self.hide()
    
    def _on_celebration_finished(self):
        """庆祝动画完成回调"""
        # 开始淡出
        self.opacity_animation.setStartValue(1.0)
        self.opacity_animation.setEndValue(0.0)
        self.opacity_animation.setDuration(500)
        self.opacity_animation.start()
    
    def paintEvent(self, event):
        """绘制事件"""
        if not self.is_celebrating:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景脉冲
        if self.pulse_intensity > 0:
            self._draw_background_pulse(painter)
        
        # 绘制粒子
        self._draw_particles(painter)
        
        # 绘制庆祝文字
        if self.text_opacity > 0:
            self._draw_celebration_text(painter)
    
    def _draw_background_pulse(self, painter: QPainter):
        """绘制背景脉冲"""
        color = QColor(Config.COLORS['success'] if self.celebration_type == "success" 
                      else Config.COLORS['danger'] if self.celebration_type == "sprint"
                      else Config.COLORS['warning'])
        color.setAlphaF(self.pulse_intensity * 0.1)
        
        painter.fillRect(self.rect(), color)
    
    def _draw_particles(self, painter: QPainter):
        """绘制粒子"""
        painter.setPen(Qt.PenStyle.NoPen)
        
        for particle in self.particles:
            painter.setBrush(QBrush(particle['color']))
            size = particle['size']
            painter.drawEllipse(
                int(particle['x'] - size/2),
                int(particle['y'] - size/2),
                int(size), int(size)
            )
    
    def _draw_celebration_text(self, painter: QPainter):
        """绘制庆祝文字"""
        painter.save()
        
        # 设置字体
        font = QFont("Arial Black", 48, QFont.Weight.Bold)
        painter.setFont(font)
        
        # 设置颜色
        color = QColor(Config.COLORS['text_light'])
        color.setAlphaF(self.text_opacity)
        painter.setPen(color)
        
        # 计算文字位置
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # 应用变换
        painter.translate(center_x, center_y)
        painter.scale(self.text_scale, self.text_scale)
        painter.rotate(self.text_rotation)
        
        # 绘制文字
        text_rect = painter.fontMetrics().boundingRect(self.celebration_text)
        painter.drawText(-text_rect.width()//2, text_rect.height()//2, self.celebration_text)
        
        painter.restore()
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 清除现有粒子，避免位置错误
        self.particles.clear()
