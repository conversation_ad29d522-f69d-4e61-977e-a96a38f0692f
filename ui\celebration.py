"""
T-1节拍器庆祝动画效果组件
"""

import math
import random
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtGui import QPainter, QColor, QFont, QPen, QBrush
from PyQt6.QtCore import Qt
from core.config import Config
from core.color_theme import ColorThemeManager

class CelebrationWidget(QWidget):
    """庆祝动画组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        self.color_manager = ColorThemeManager()
        self.celebration_colors = self.color_manager.get_celebration_colors()

        # 动画状态
        self.is_celebrating = False
        self.celebration_type = "success"  # success, milestone
        self.continuous_fireworks = False  # 持续烟花标志

        # 粒子系统
        self.particles = []
        self.fireworks = []
        
        # 文字动画
        self.text_scale = 0.0
        self.text_rotation = 0.0
        self.text_opacity = 0.0
        self.celebration_text = ""
        
        # 定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._update_animation)
        
        # 属性动画
        self.text_animation = QPropertyAnimation(self, b"textScale")
        self.text_animation.setDuration(Config.ANIMATION['celebration_duration'])
        self.text_animation.setEasingCurve(QEasingCurve.Type.OutElastic)
        
        self.opacity_animation = QPropertyAnimation(self, b"textOpacity")
        self.opacity_animation.setDuration(Config.ANIMATION['celebration_duration'])
        
        # 背景脉冲
        self.pulse_intensity = 0.0
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self._update_pulse)
        
        self.setStyleSheet("background: transparent;")
        self.hide()
    
    @pyqtProperty(float)
    def textScale(self):
        return self.text_scale
    
    @textScale.setter
    def textScale(self, value):
        self.text_scale = value
        self.update()
    
    @pyqtProperty(float)
    def textOpacity(self):
        return self.text_opacity
    
    @textOpacity.setter
    def textOpacity(self, value):
        self.text_opacity = value
        self.update()
    
    def celebrate_success(self):
        """触发成功庆祝动画 - 烟花绽放"""
        self.celebration_type = "success"
        self.celebration_text = "目标达成!"
        self._start_fireworks_celebration()
    
    def celebrate_milestone(self, percentage: float):
        """触发里程碑庆祝动画"""
        self.celebration_type = "milestone"
        self.celebration_text = f"{int(percentage)}% 完成!"
        self._start_mini_celebration()
    
    def celebrate_sprint_mode(self):
        """触发冲刺模式动画"""
        self.celebration_type = "sprint"
        self.celebration_text = "冲刺模式!"
        self._start_warning_animation()
    
    def _start_celebration(self):
        """开始完整庆祝动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建烟花粒子
        self._create_fireworks()
        
        # 启动动画定时器
        self.animation_timer.start(50)  # 20 FPS
        
        # 文字动画
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.5)
        self.text_animation.finished.connect(self._on_celebration_finished)
        self.text_animation.start()
        
        # 透明度动画
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        # 背景脉冲
        self.pulse_timer.start(100)
        
        # 3秒后自动结束
        QTimer.singleShot(Config.ANIMATION['celebration_duration'], self._stop_celebration)
    
    def _start_mini_celebration(self):
        """开始小型庆祝动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建少量粒子
        self._create_mini_particles()
        
        self.animation_timer.start(50)
        
        # 简短的文字动画
        self.text_animation.setDuration(1000)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.0)
        self.text_animation.start()
        
        self.opacity_animation.setDuration(1000)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(0.8)
        self.opacity_animation.start()
        
        # 1秒后结束
        QTimer.singleShot(1000, self._stop_celebration)
    
    def _start_warning_animation(self):
        """开始警告动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建警告粒子
        self._create_warning_particles()
        
        self.animation_timer.start(50)
        
        # 快速闪烁动画
        self.text_animation.setDuration(500)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.2)
        self.text_animation.start()
        
        self.opacity_animation.setDuration(500)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        # 快速脉冲
        self.pulse_timer.start(50)
        
        QTimer.singleShot(1500, self._stop_celebration)

    def _start_fireworks_celebration(self):
        """开始烟花庆祝动画 - 持续到下一个周期"""
        self.is_celebrating = True
        self.show()

        # 设置为持续模式
        self.continuous_fireworks = True

        # 启动动画定时器
        self.animation_timer.start(50)  # 20 FPS

        # 文字动画
        self.text_animation.setDuration(2000)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.5)
        self.text_animation.start()

        # 透明度动画
        self.opacity_animation.setDuration(2000)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()

        # 背景脉冲
        self.pulse_timer.start(100)

        # 启动烟花发射定时器
        self.fireworks_timer = QTimer()
        self.fireworks_timer.timeout.connect(self._launch_firework)
        self.fireworks_timer.start(500)  # 每0.5秒发射一次烟花，更频繁

        # 启动多重烟花定时器
        self.multi_fireworks_timer = QTimer()
        self.multi_fireworks_timer.timeout.connect(self._launch_multiple_fireworks)
        self.multi_fireworks_timer.start(1500)  # 每1.5秒发射多重烟花

        # 初始烟花
        self._launch_multiple_fireworks()

        print("🎆 烟花庆祝开始！将持续到下一个周期开始...")

    def stop_fireworks_celebration(self):
        """停止烟花庆祝动画"""
        if hasattr(self, 'continuous_fireworks') and self.continuous_fireworks:
            self.continuous_fireworks = False
            if hasattr(self, 'fireworks_timer'):
                self.fireworks_timer.stop()
            if hasattr(self, 'multi_fireworks_timer'):
                self.multi_fireworks_timer.stop()
            self._stop_celebration()
            print("🎆 烟花庆祝结束")

    def _launch_firework(self):
        """发射单个烟花"""
        if not self.continuous_fireworks:
            return

        center_x = self.width() // 2
        center_y = self.height() // 2

        # 随机选择爆炸点
        explosion_x = center_x + random.randint(-100, 100)
        explosion_y = center_y + random.randint(-80, 80)

        # 随机选择烟花颜色
        firework_colors = [
            QColor("#FFD700"),  # 金色
            QColor("#FF6B6B"),  # 红色
            QColor("#4CCD99"),  # 绿色
            QColor("#2196F3"),  # 蓝色
            QColor("#9C27B0"),  # 紫色
            QColor("#FF9800"),  # 橙色
            QColor("#00BCD4"),  # 青色
            QColor("#FFEB3B"),  # 黄色
        ]

        firework_color = random.choice(firework_colors)

        # 创建烟花粒子 - 增加数量和效果
        particle_count = random.randint(25, 40)  # 增加粒子数量
        for i in range(particle_count):
            angle = (i / particle_count) * 2 * math.pi + random.uniform(-0.3, 0.3)
            speed = random.uniform(3, 8)  # 增加速度范围

            particle = {
                'x': explosion_x,
                'y': explosion_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(4, 12),  # 增加粒子大小
                'life': 1.0,
                'color': QColor(firework_color),
                'gravity': random.uniform(0.03, 0.12),
                'fade_rate': random.uniform(0.008, 0.015),  # 减慢消失速度
                'sparkle': random.random() < 0.5,  # 50%概率闪烁
                'trail': [],  # 添加拖尾效果
                'glow': random.uniform(0.5, 1.0)  # 发光强度
            }
            self.particles.append(particle)

    def _launch_multiple_fireworks(self):
        """发射多个烟花"""
        if not self.continuous_fireworks:
            return

        # 发射更多烟花，创造壮观效果
        firework_count = random.randint(4, 7)
        for _ in range(firework_count):
            self._launch_firework()

        # 添加特殊的大型烟花
        self._launch_grand_firework()

    def _launch_grand_firework(self):
        """发射大型烟花"""
        if not self.continuous_fireworks:
            return

        center_x = self.width() // 2
        center_y = self.height() // 2

        # 大型烟花在中心附近爆炸
        explosion_x = center_x + random.randint(-50, 50)
        explosion_y = center_y + random.randint(-30, 30)

        # 特殊颜色组合
        grand_colors = [
            QColor("#FFD700"),  # 金色
            QColor("#FFFFFF"),  # 白色
            QColor("#FF1493"),  # 深粉色
        ]

        # 创建大型烟花粒子
        particle_count = random.randint(50, 80)  # 更多粒子
        for i in range(particle_count):
            angle = (i / particle_count) * 2 * math.pi + random.uniform(-0.2, 0.2)
            speed = random.uniform(4, 10)  # 更高速度

            # 随机选择颜色
            firework_color = random.choice(grand_colors)

            particle = {
                'x': explosion_x,
                'y': explosion_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(6, 15),  # 更大粒子
                'life': 1.0,
                'color': QColor(firework_color),
                'gravity': random.uniform(0.02, 0.08),  # 更慢的重力
                'fade_rate': random.uniform(0.005, 0.012),  # 更慢消失
                'sparkle': random.random() < 0.7,  # 70%概率闪烁
                'trail': [],
                'glow': random.uniform(0.8, 1.0)  # 更强发光
            }
            self.particles.append(particle)

    def _create_fireworks(self):
        """创建烟花效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # 创建多个烟花爆炸点
        for _ in range(3):
            explosion_x = center_x + random.randint(-200, 200)
            explosion_y = center_y + random.randint(-150, 150)
            
            # 每个爆炸点创建粒子
            for i in range(30):
                angle = (i / 30) * 2 * math.pi
                speed = random.uniform(3, 8)
                
                particle = {
                    'x': explosion_x,
                    'y': explosion_y,
                    'vx': speed * math.cos(angle),
                    'vy': speed * math.sin(angle),
                    'size': random.uniform(4, 12),
                    'life': 1.0,
                    'color': QColor(random.choice(self.celebration_colors)),
                    'gravity': 0.1
                }
                self.particles.append(particle)
    
    def _create_mini_particles(self):
        """创建小型粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i in range(10):
            angle = (i / 10) * 2 * math.pi
            speed = random.uniform(2, 4)
            
            particle = {
                'x': center_x,
                'y': center_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(3, 6),
                'life': 1.0,
                'color': QColor(Config.COLORS['warning']),
                'gravity': 0.05
            }
            self.particles.append(particle)
    
    def _create_warning_particles(self):
        """创建警告粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i in range(15):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(3, 6)
            
            particle = {
                'x': center_x,
                'y': center_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(4, 8),
                'life': 1.0,
                'color': QColor(Config.COLORS['danger']),
                'gravity': 0.08
            }
            self.particles.append(particle)
    
    def _update_animation(self):
        """更新动画状态"""
        # 更新粒子
        for particle in self.particles[:]:
            # 保存当前位置到拖尾
            if 'trail' in particle:
                particle['trail'].append((particle['x'], particle['y'], particle['life']))
                # 限制拖尾长度
                if len(particle['trail']) > 8:
                    particle['trail'].pop(0)

            particle['x'] += particle['vx']
            particle['y'] += particle['vy']

            # 重力效果
            if 'gravity' in particle:
                particle['vy'] += particle['gravity']
            else:
                particle['vy'] += 0.1  # 默认重力

            # 空气阻力
            particle['vx'] *= 0.995
            particle['vy'] *= 0.995

            # 生命值衰减
            if 'fade_rate' in particle:
                particle['life'] -= particle['fade_rate']
            else:
                particle['life'] -= 0.02

            # 大小变化 - 更慢的衰减
            particle['size'] *= 0.996

            # 增强闪烁效果
            if particle.get('sparkle', False):
                # 更强烈的闪烁效果
                sparkle_alpha = (math.sin(particle['life'] * 25) + 1) / 2
                glow_factor = particle.get('glow', 1.0)
                alpha_value = max(0.0, min(1.0, particle['life'] * sparkle_alpha * glow_factor))
                particle['color'].setAlphaF(alpha_value)
            else:
                # 普通粒子的透明度
                glow_factor = particle.get('glow', 1.0)
                alpha_value = max(0.0, min(1.0, particle['life'] * glow_factor))
                particle['color'].setAlphaF(alpha_value)

            # 移除死亡粒子
            if particle['life'] <= 0 or particle['size'] < 0.5:
                self.particles.remove(particle)

        # 更新文字旋转
        if self.celebration_type == "success":
            self.text_rotation += 1  # 减慢旋转速度

        # 限制粒子数量以保持性能
        if len(self.particles) > 200:
            # 移除最老的粒子
            self.particles = self.particles[-150:]

        self.update()
    
    def _update_pulse(self):
        """更新脉冲效果"""
        import time
        if self.celebration_type == "sprint":
            # 快速脉冲
            self.pulse_intensity = (math.sin(time.time() * 10) + 1) / 2
        else:
            # 慢速脉冲
            self.pulse_intensity = (math.sin(time.time() * 3) + 1) / 2
        
        self.update()
    
    def _stop_celebration(self):
        """停止庆祝动画"""
        self.is_celebrating = False
        self.animation_timer.stop()
        self.pulse_timer.stop()
        self.particles.clear()
        self.hide()
    
    def _on_celebration_finished(self):
        """庆祝动画完成回调"""
        # 开始淡出
        self.opacity_animation.setStartValue(1.0)
        self.opacity_animation.setEndValue(0.0)
        self.opacity_animation.setDuration(500)
        self.opacity_animation.start()
    
    def paintEvent(self, event):
        """绘制事件"""
        if not self.is_celebrating or not self.isVisible():
            return

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景脉冲
        if self.pulse_intensity > 0:
            self._draw_background_pulse(painter)

        # 绘制粒子
        self._draw_particles(painter)

        # 绘制庆祝文字
        if self.text_opacity > 0:
            self._draw_celebration_text(painter)
    
    def _draw_background_pulse(self, painter: QPainter):
        """绘制背景脉冲"""
        color = QColor(Config.COLORS['success'] if self.celebration_type == "success" 
                      else Config.COLORS['danger'] if self.celebration_type == "sprint"
                      else Config.COLORS['warning'])
        color.setAlphaF(self.pulse_intensity * 0.1)
        
        painter.fillRect(self.rect(), color)
    
    def _draw_particles(self, painter: QPainter):
        """绘制粒子（增强版）"""
        painter.setPen(Qt.PenStyle.NoPen)

        for particle in self.particles:
            # 绘制拖尾效果
            if 'trail' in particle and len(particle['trail']) > 1:
                self._draw_particle_trail(painter, particle)

            # 绘制发光效果
            if particle.get('glow', 0) > 0.5:
                self._draw_particle_glow(painter, particle)

            # 绘制主粒子
            painter.setBrush(QBrush(particle['color']))
            size = particle['size']
            painter.drawEllipse(
                int(particle['x'] - size/2),
                int(particle['y'] - size/2),
                int(size), int(size)
            )

    def _draw_particle_trail(self, painter: QPainter, particle):
        """绘制粒子拖尾"""
        trail = particle['trail']
        if len(trail) < 2:
            return

        for i, (x, y, life) in enumerate(trail):
            # 拖尾透明度递减
            alpha = (i / len(trail)) * particle['color'].alphaF() * 0.6
            trail_color = QColor(particle['color'])
            trail_color.setAlphaF(alpha)

            painter.setBrush(QBrush(trail_color))
            # 拖尾大小递减
            trail_size = particle['size'] * (i / len(trail)) * 0.8
            painter.drawEllipse(
                int(x - trail_size/2),
                int(y - trail_size/2),
                int(trail_size), int(trail_size)
            )

    def _draw_particle_glow(self, painter: QPainter, particle):
        """绘制粒子发光效果"""
        glow_color = QColor(particle['color'])
        glow_color.setAlphaF(particle['color'].alphaF() * 0.3)

        painter.setBrush(QBrush(glow_color))
        glow_size = particle['size'] * 2.5
        painter.drawEllipse(
            int(particle['x'] - glow_size/2),
            int(particle['y'] - glow_size/2),
            int(glow_size), int(glow_size)
        )
    
    def _draw_celebration_text(self, painter: QPainter):
        """绘制庆祝文字"""
        painter.save()
        
        # 设置字体
        font = QFont("Arial Black", 48, QFont.Weight.Bold)
        painter.setFont(font)
        
        # 设置颜色
        color = QColor(Config.COLORS['text_light'])
        color.setAlphaF(self.text_opacity)
        painter.setPen(color)
        
        # 计算文字位置
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # 应用变换
        painter.translate(center_x, center_y)
        painter.scale(self.text_scale, self.text_scale)
        painter.rotate(self.text_rotation)
        
        # 绘制文字
        text_rect = painter.fontMetrics().boundingRect(self.celebration_text)
        painter.drawText(-text_rect.width()//2, text_rect.height()//2, self.celebration_text)
        
        painter.restore()
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 清除现有粒子，避免位置错误
        self.particles.clear()
