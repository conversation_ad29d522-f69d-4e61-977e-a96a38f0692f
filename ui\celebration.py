"""
T-1节拍器庆祝动画效果组件
"""

import math
import random
from PyQt6.QtWidgets import QWidget
from PyQt6.QtCore import QTimer, QPropertyAnimation, QEasingCurve, pyqtProperty
from PyQt6.QtGui import QPainter, QColor, QFont, QPen, QBrush
from PyQt6.QtCore import Qt
from core.config import Config
from core.color_theme import ColorThemeManager

class CelebrationWidget(QWidget):
    """庆祝动画组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.color_manager = ColorThemeManager()
        self.celebration_colors = self.color_manager.get_celebration_colors()
        
        # 动画状态
        self.is_celebrating = False
        self.celebration_type = "success"  # success, milestone
        
        # 粒子系统
        self.particles = []
        self.fireworks = []
        
        # 文字动画
        self.text_scale = 0.0
        self.text_rotation = 0.0
        self.text_opacity = 0.0
        self.celebration_text = ""
        
        # 定时器
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self._update_animation)
        
        # 属性动画
        self.text_animation = QPropertyAnimation(self, b"textScale")
        self.text_animation.setDuration(Config.ANIMATION['celebration_duration'])
        self.text_animation.setEasingCurve(QEasingCurve.Type.OutElastic)
        
        self.opacity_animation = QPropertyAnimation(self, b"textOpacity")
        self.opacity_animation.setDuration(Config.ANIMATION['celebration_duration'])
        
        # 背景脉冲
        self.pulse_intensity = 0.0
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self._update_pulse)
        
        self.setStyleSheet("background: transparent;")
        self.hide()
    
    @pyqtProperty(float)
    def textScale(self):
        return self.text_scale
    
    @textScale.setter
    def textScale(self, value):
        self.text_scale = value
        self.update()
    
    @pyqtProperty(float)
    def textOpacity(self):
        return self.text_opacity
    
    @textOpacity.setter
    def textOpacity(self, value):
        self.text_opacity = value
        self.update()
    
    def celebrate_success(self):
        """触发成功庆祝动画"""
        self.celebration_type = "success"
        self.celebration_text = "目标达成!"
        self._start_celebration()
    
    def celebrate_milestone(self, percentage: float):
        """触发里程碑庆祝动画"""
        self.celebration_type = "milestone"
        self.celebration_text = f"{int(percentage)}% 完成!"
        self._start_mini_celebration()
    
    def celebrate_sprint_mode(self):
        """触发冲刺模式动画"""
        self.celebration_type = "sprint"
        self.celebration_text = "冲刺模式!"
        self._start_warning_animation()
    
    def _start_celebration(self):
        """开始完整庆祝动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建烟花粒子
        self._create_fireworks()
        
        # 启动动画定时器
        self.animation_timer.start(50)  # 20 FPS
        
        # 文字动画
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.5)
        self.text_animation.finished.connect(self._on_celebration_finished)
        self.text_animation.start()
        
        # 透明度动画
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        # 背景脉冲
        self.pulse_timer.start(100)
        
        # 3秒后自动结束
        QTimer.singleShot(Config.ANIMATION['celebration_duration'], self._stop_celebration)
    
    def _start_mini_celebration(self):
        """开始小型庆祝动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建少量粒子
        self._create_mini_particles()
        
        self.animation_timer.start(50)
        
        # 简短的文字动画
        self.text_animation.setDuration(1000)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.0)
        self.text_animation.start()
        
        self.opacity_animation.setDuration(1000)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(0.8)
        self.opacity_animation.start()
        
        # 1秒后结束
        QTimer.singleShot(1000, self._stop_celebration)
    
    def _start_warning_animation(self):
        """开始警告动画"""
        self.is_celebrating = True
        self.show()
        
        # 创建警告粒子
        self._create_warning_particles()
        
        self.animation_timer.start(50)
        
        # 快速闪烁动画
        self.text_animation.setDuration(500)
        self.text_animation.setStartValue(0.0)
        self.text_animation.setEndValue(1.2)
        self.text_animation.start()
        
        self.opacity_animation.setDuration(500)
        self.opacity_animation.setStartValue(0.0)
        self.opacity_animation.setEndValue(1.0)
        self.opacity_animation.start()
        
        # 快速脉冲
        self.pulse_timer.start(50)
        
        QTimer.singleShot(1500, self._stop_celebration)
    
    def _create_fireworks(self):
        """创建烟花效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # 创建多个烟花爆炸点
        for _ in range(3):
            explosion_x = center_x + random.randint(-200, 200)
            explosion_y = center_y + random.randint(-150, 150)
            
            # 每个爆炸点创建粒子
            for i in range(30):
                angle = (i / 30) * 2 * math.pi
                speed = random.uniform(3, 8)
                
                particle = {
                    'x': explosion_x,
                    'y': explosion_y,
                    'vx': speed * math.cos(angle),
                    'vy': speed * math.sin(angle),
                    'size': random.uniform(4, 12),
                    'life': 1.0,
                    'color': QColor(random.choice(self.celebration_colors)),
                    'gravity': 0.1
                }
                self.particles.append(particle)
    
    def _create_mini_particles(self):
        """创建小型粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i in range(10):
            angle = (i / 10) * 2 * math.pi
            speed = random.uniform(2, 4)
            
            particle = {
                'x': center_x,
                'y': center_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(3, 6),
                'life': 1.0,
                'color': QColor(Config.COLORS['warning']),
                'gravity': 0.05
            }
            self.particles.append(particle)
    
    def _create_warning_particles(self):
        """创建警告粒子效果"""
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        for i in range(15):
            angle = random.uniform(0, 2 * math.pi)
            speed = random.uniform(3, 6)
            
            particle = {
                'x': center_x,
                'y': center_y,
                'vx': speed * math.cos(angle),
                'vy': speed * math.sin(angle),
                'size': random.uniform(4, 8),
                'life': 1.0,
                'color': QColor(Config.COLORS['danger']),
                'gravity': 0.08
            }
            self.particles.append(particle)
    
    def _update_animation(self):
        """更新动画状态"""
        # 更新粒子
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['vy'] += particle['gravity']  # 重力效果
            
            particle['life'] -= 0.02
            particle['size'] *= 0.99
            
            # 更新颜色透明度
            color = particle['color']
            color.setAlphaF(particle['life'])
            
            if particle['life'] <= 0 or particle['size'] < 1:
                self.particles.remove(particle)
        
        # 更新文字旋转
        if self.celebration_type == "success":
            self.text_rotation += 2
        
        self.update()
    
    def _update_pulse(self):
        """更新脉冲效果"""
        import time
        if self.celebration_type == "sprint":
            # 快速脉冲
            self.pulse_intensity = (math.sin(time.time() * 10) + 1) / 2
        else:
            # 慢速脉冲
            self.pulse_intensity = (math.sin(time.time() * 3) + 1) / 2
        
        self.update()
    
    def _stop_celebration(self):
        """停止庆祝动画"""
        self.is_celebrating = False
        self.animation_timer.stop()
        self.pulse_timer.stop()
        self.particles.clear()
        self.hide()
    
    def _on_celebration_finished(self):
        """庆祝动画完成回调"""
        # 开始淡出
        self.opacity_animation.setStartValue(1.0)
        self.opacity_animation.setEndValue(0.0)
        self.opacity_animation.setDuration(500)
        self.opacity_animation.start()
    
    def paintEvent(self, event):
        """绘制事件"""
        if not self.is_celebrating:
            return
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 绘制背景脉冲
        if self.pulse_intensity > 0:
            self._draw_background_pulse(painter)
        
        # 绘制粒子
        self._draw_particles(painter)
        
        # 绘制庆祝文字
        if self.text_opacity > 0:
            self._draw_celebration_text(painter)
    
    def _draw_background_pulse(self, painter: QPainter):
        """绘制背景脉冲"""
        color = QColor(Config.COLORS['success'] if self.celebration_type == "success" 
                      else Config.COLORS['danger'] if self.celebration_type == "sprint"
                      else Config.COLORS['warning'])
        color.setAlphaF(self.pulse_intensity * 0.1)
        
        painter.fillRect(self.rect(), color)
    
    def _draw_particles(self, painter: QPainter):
        """绘制粒子"""
        painter.setPen(Qt.PenStyle.NoPen)
        
        for particle in self.particles:
            painter.setBrush(QBrush(particle['color']))
            size = particle['size']
            painter.drawEllipse(
                int(particle['x'] - size/2),
                int(particle['y'] - size/2),
                int(size), int(size)
            )
    
    def _draw_celebration_text(self, painter: QPainter):
        """绘制庆祝文字"""
        painter.save()
        
        # 设置字体
        font = QFont("Arial Black", 48, QFont.Weight.Bold)
        painter.setFont(font)
        
        # 设置颜色
        color = QColor(Config.COLORS['text_light'])
        color.setAlphaF(self.text_opacity)
        painter.setPen(color)
        
        # 计算文字位置
        center_x = self.width() // 2
        center_y = self.height() // 2
        
        # 应用变换
        painter.translate(center_x, center_y)
        painter.scale(self.text_scale, self.text_scale)
        painter.rotate(self.text_rotation)
        
        # 绘制文字
        text_rect = painter.fontMetrics().boundingRect(self.celebration_text)
        painter.drawText(-text_rect.width()//2, text_rect.height()//2, self.celebration_text)
        
        painter.restore()
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 清除现有粒子，避免位置错误
        self.particles.clear()
