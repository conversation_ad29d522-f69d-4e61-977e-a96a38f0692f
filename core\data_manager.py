"""
T-1节拍器数据管理模块
生产数据模拟和管理
"""

import random
import time
import threading
from datetime import datetime, timedelta
from typing import Callable, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from .config import Config

class ProductionDataManager(QObject):
    """生产数据管理器"""
    
    # 信号定义
    data_updated = pyqtSignal(int, int, int)  # current_count, target, time_remaining
    cycle_completed = pyqtSignal(int, int)    # final_count, target
    milestone_reached = pyqtSignal(float)     # progress_percentage
    
    def __init__(self):
        super().__init__()
        self.current_count = 0
        self.target = Config.DEFAULT_TARGET
        self.is_running = True  # 程序启动后自动开始

        # 基于系统时间的周期管理
        self.cycle_start_time = None
        self.current_cycle_end = None
        self.last_milestone = 0  # 上次里程碑

        # 定时器 - 只用于周期检查和界面更新
        self.cycle_timer = QTimer()
        self.cycle_timer.timeout.connect(self._check_cycle)

        # 界面更新定时器
        self.ui_update_timer = QTimer()
        self.ui_update_timer.timeout.connect(self._emit_update)

        # 初始化当前周期并自动开始
        self._initialize_current_cycle()
        self._auto_start()

    def _auto_start(self):
        """程序启动后自动开始监控"""
        self.cycle_start_time = datetime.now()
        self.is_running = True

        # 启动定时器
        self.ui_update_timer.start(Config.UPDATE_INTERVAL)
        self.cycle_timer.start(1000)  # 每秒检查周期

        print(f"自动开始生产监控: 目标产量 {self.target}")

    def _initialize_current_cycle(self):
        """初始化当前10分钟周期"""
        now = datetime.now()
        # 计算当前10分钟周期的开始时间
        minutes = (now.minute // 10) * 10
        cycle_start = now.replace(minute=minutes, second=0, microsecond=0)
        cycle_end = cycle_start + timedelta(minutes=10)

        self.current_cycle_end = cycle_end
        print(f"当前周期: {cycle_start.strftime('%H:%M')} - {cycle_end.strftime('%H:%M')}")

    def get_current_cycle_info(self):
        """获取当前周期信息"""
        if not self.current_cycle_end:
            self._initialize_current_cycle()

        now = datetime.now()
        if now >= self.current_cycle_end:
            # 当前周期已结束，初始化新周期
            self._initialize_current_cycle()
            if self.is_running:
                # 自动重置产量计数
                self._reset_for_new_cycle()

        time_remaining = (self.current_cycle_end - now).total_seconds()
        return max(0, int(time_remaining))

    def _reset_for_new_cycle(self):
        """为新周期重置数据"""
        old_count = self.current_count
        self.current_count = 0
        self.last_milestone = 0
        print(f"新周期开始，产量重置: {old_count} → 0")

        # 发送数据更新信号
        self._emit_update()

    def _check_cycle(self):
        """检查周期状态"""
        time_remaining = self.get_current_cycle_info()
        if time_remaining == 0 and self.is_running:
            # 周期结束，触发完成事件
            self.cycle_completed.emit(self.current_count, self.target)

    def restart_cycle(self):
        """重新开始当前周期（重置计数）"""
        self.current_count = 0
        self.last_milestone = 0
        self.cycle_start_time = datetime.now()
        print(f"重新开始周期: 目标产量 {self.target}")
        self._emit_update()
    
    def pause_cycle(self):
        """暂停周期"""
        if self.is_running:
            self.update_timer.stop()
            self.countdown_timer.stop()
    
    def resume_cycle(self):
        """恢复周期"""
        if self.is_running:
            self.update_timer.start(Config.UPDATE_INTERVAL)
            self.countdown_timer.start(1000)
    
    def set_target(self, target: int):
        """设置目标产量"""
        self.target = max(1, target)
        self._emit_update()
    
    def add_manual_count(self, count: int = 1):
        """手动添加产量"""
        self.current_count += count
        self._check_milestones()
        self._emit_update()
    
    # 移除模拟数据更新功能，只保留手动计数和COM8数据接收
    
    def _emit_update(self):
        """发送数据更新信号"""
        time_remaining = self.get_current_cycle_info()
        self.data_updated.emit(self.current_count, self.target, time_remaining)
    
    def _check_milestones(self):
        """检查里程碑"""
        if self.target == 0:
            return
        
        progress = (self.current_count / self.target) * 100
        
        # 检查10%的里程碑
        milestone = int(progress // 10) * 10
        if milestone > self.last_milestone and milestone > 0:
            self.last_milestone = milestone
            self.milestone_reached.emit(progress)
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.target == 0:
            return 0.0
        return min(100.0, (self.current_count / self.target) * 100)

    def get_time_formatted(self) -> str:
        """获取格式化的剩余时间"""
        time_remaining = self.get_current_cycle_info()
        minutes = time_remaining // 60
        seconds = time_remaining % 60
        return f"{minutes:02d}:{seconds:02d}"

    def get_seconds_per_item(self) -> float:
        """获取平均每件耗时（秒/件）"""
        if not self.cycle_start_time or self.current_count == 0:
            return 0.0

        elapsed = (datetime.now() - self.cycle_start_time).total_seconds()
        return elapsed / self.current_count

    def get_current_time_formatted(self) -> str:
        """获取当前系统时间"""
        return datetime.now().strftime("%H:%M:%S")

    def get_estimated_final_count(self) -> int:
        """估算最终产量"""
        time_remaining = self.get_current_cycle_info()
        if time_remaining == 0 or not self.cycle_start_time:
            return self.current_count

        elapsed = (datetime.now() - self.cycle_start_time).total_seconds()
        if elapsed > 0:
            current_rate = self.current_count / elapsed  # 件/秒
            estimated_additional = current_rate * time_remaining
            return int(self.current_count + estimated_additional)

        return self.current_count
