"""
T-1节拍器数据管理模块
生产数据模拟和管理
"""

import random
import time
import threading
from typing import Callable, Optional
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from .config import Config

class ProductionDataManager(QObject):
    """生产数据管理器"""
    
    # 信号定义
    data_updated = pyqtSignal(int, int, int)  # current_count, target, time_remaining
    cycle_completed = pyqtSignal(int, int)    # final_count, target
    milestone_reached = pyqtSignal(float)     # progress_percentage
    
    def __init__(self):
        super().__init__()
        self.current_count = 0
        self.target = Config.DEFAULT_TARGET
        self.time_remaining = Config.COUNTDOWN_DURATION
        self.is_running = False
        self.simulation_mode = True
        
        # 模拟参数
        self.base_rate = 0.15  # 基础生产速率 (件/秒)
        self.rate_variance = 0.05  # 速率变化幅度
        self.burst_probability = 0.1  # 突发生产概率
        self.last_milestone = 0  # 上次里程碑
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_data)
        
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self._countdown_tick)
    
    def start_cycle(self, target: Optional[int] = None):
        """
        开始新的生产周期
        
        Args:
            target: 目标产量，None则使用默认值
        """
        if target is not None:
            self.target = target
        
        self.current_count = 0
        self.time_remaining = Config.COUNTDOWN_DURATION
        self.last_milestone = 0
        self.is_running = True
        
        # 启动定时器
        self.update_timer.start(Config.UPDATE_INTERVAL)
        self.countdown_timer.start(1000)  # 每秒倒计时
        
        print(f"开始新周期: 目标产量 {self.target}")
    
    def stop_cycle(self):
        """停止当前周期"""
        self.is_running = False
        self.update_timer.stop()
        self.countdown_timer.stop()
        
        # 发送周期完成信号
        self.cycle_completed.emit(self.current_count, self.target)
        print(f"周期结束: 实际产量 {self.current_count}/{self.target}")
    
    def pause_cycle(self):
        """暂停周期"""
        if self.is_running:
            self.update_timer.stop()
            self.countdown_timer.stop()
    
    def resume_cycle(self):
        """恢复周期"""
        if self.is_running:
            self.update_timer.start(Config.UPDATE_INTERVAL)
            self.countdown_timer.start(1000)
    
    def set_target(self, target: int):
        """设置目标产量"""
        self.target = max(1, target)
        self._emit_update()
    
    def add_manual_count(self, count: int = 1):
        """手动添加产量"""
        self.current_count += count
        self._check_milestones()
        self._emit_update()
    
    def set_simulation_mode(self, enabled: bool):
        """设置模拟模式"""
        self.simulation_mode = enabled
    
    def _update_data(self):
        """更新生产数据（模拟模式）"""
        if not self.simulation_mode or not self.is_running:
            return
        
        # 计算当前生产速率
        current_rate = self._calculate_production_rate()
        
        # 根据速率增加产量
        increment = self._calculate_increment(current_rate)
        if increment > 0:
            self.current_count += increment
            self._check_milestones()
        
        self._emit_update()
    
    def _calculate_production_rate(self) -> float:
        """计算当前生产速率"""
        # 基础速率
        rate = self.base_rate
        
        # 添加随机变化
        variance = random.uniform(-self.rate_variance, self.rate_variance)
        rate += variance
        
        # 时间压力因子（剩余时间越少，速率可能越高）
        time_factor = 1.0
        if self.time_remaining < 120:  # 最后2分钟
            progress = self.current_count / self.target if self.target > 0 else 0
            if progress < 0.8:  # 进度不足80%时加速
                time_factor = 1.5
        
        # 突发生产
        if random.random() < self.burst_probability:
            rate *= 2.0
        
        return max(0, rate * time_factor)
    
    def _calculate_increment(self, rate: float) -> int:
        """根据速率计算产量增量"""
        # 每秒的期望增量
        expected_increment = rate * (Config.UPDATE_INTERVAL / 1000.0)
        
        # 使用泊松分布模拟离散事件
        if expected_increment > 0:
            # 简化的泊松分布近似
            if random.random() < expected_increment:
                return 1
            elif expected_increment > 1 and random.random() < (expected_increment - 1):
                return 2
        
        return 0
    
    def _countdown_tick(self):
        """倒计时更新"""
        if self.time_remaining > 0:
            self.time_remaining -= 1
        else:
            # 时间到，结束周期
            self.stop_cycle()
    
    def _check_milestones(self):
        """检查里程碑"""
        if self.target == 0:
            return
        
        progress = (self.current_count / self.target) * 100
        
        # 检查10%的里程碑
        milestone = int(progress // 10) * 10
        if milestone > self.last_milestone and milestone > 0:
            self.last_milestone = milestone
            self.milestone_reached.emit(progress)
    
    def _emit_update(self):
        """发送数据更新信号"""
        self.data_updated.emit(self.current_count, self.target, self.time_remaining)
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if self.target == 0:
            return 0.0
        return min(100.0, (self.current_count / self.target) * 100)
    
    def get_time_formatted(self) -> str:
        """获取格式化的剩余时间"""
        minutes = self.time_remaining // 60
        seconds = self.time_remaining % 60
        return f"{minutes:02d}:{seconds:02d}"
    
    def get_production_rate(self) -> float:
        """获取当前生产速率（件/分钟）"""
        elapsed = Config.COUNTDOWN_DURATION - self.time_remaining
        if elapsed > 0:
            return (self.current_count / elapsed) * 60
        return 0.0
    
    def get_estimated_final_count(self) -> int:
        """估算最终产量"""
        if self.time_remaining == 0:
            return self.current_count
        
        current_rate = self.get_production_rate() / 60  # 转换为件/秒
        estimated_additional = current_rate * self.time_remaining
        return int(self.current_count + estimated_additional)
